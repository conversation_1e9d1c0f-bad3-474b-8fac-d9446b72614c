import { useSearchParams } from '@umijs/max';
import { Flex } from 'antd';
import { createStyles } from 'antd-style';
import { MouseEvent } from 'react';
import useQueryReferenceMenu from '../hooks/useQueryReferenceMenu';

const useStyles = createStyles(({ css }) => ({
  rightSider: css`
    height: calc(100vh - 58px);
    margin-top: 10px;
    padding: 10px 20px 0;
    border-left: 1px solid rgba(229, 229, 229, 1);
    background-color: #fff;
    overflow: hidden;
    h1 {
      font-size: 24px;
      font-weight: 500;
    }
  `,
  otherResource: css`
    width: 100%;
    padding: 20px 15px;
    border-radius: 10px;
    border: 1px solid rgba(229, 229, 229, 1);
  `,
  otherResourceItem: css`
    margin-bottom: 8px;
    cursor: pointer;
    color: rgba(17, 75, 237, 1);
    font-weight: 600;
    font-size: 14px;
    &:hover {
      text-decoration: underline;
    }
    &:last-child {
      margin-bottom: 0;
    }
  `,
}));

const RightSider = () => {
  const { styles } = useStyles();
  const [, setSearchParams] = useSearchParams();
  const { data, loading } = useQueryReferenceMenu();
  const handleClick = (
    e: MouseEvent<HTMLDivElement, globalThis.MouseEvent>,
    key: number,
  ) => {
    setSearchParams(
      `?m=${key}&d=${(e.nativeEvent.target as HTMLElement).dataset.documentid}`,
    );
  };
  return (
    <div className={styles.rightSider}>
      <h1>其他资源</h1>
      <Flex vertical gap={10} className={styles.otherResource}>
        {loading ? (
          <div>加载中...</div>
        ) : (
          data?.map((item, index) => (
            <div
              key={item.Id}
              className={styles.otherResourceItem}
              data-documentid={item.DocumentId}
              onClick={(e) => handleClick(e, item.Id)}
            >
              {item.Name}
            </div>
          ))
        )}
      </Flex>
    </div>
  );
};

export default RightSider;
