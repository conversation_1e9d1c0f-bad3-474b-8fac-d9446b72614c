import Banner from '@/components/Banner';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import LeftMenu from '@/components/LeftMenu';
import { Outlet, useLocation } from '@umijs/max';
import { FloatButton } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';

const useStyles = createStyles(({ css }) => ({
  content: css`
    min-height: 100vh;
  `,
  container: {
    width: '100%',
    display: 'flex',
    // justifyContent: 'space-between',
    marginTop: 20,
  },
  left: {
    width: '25%',
    flex: 'none',
  },
  center: {
    width: '70%',
    flex: 'none',
  },
}));

const BaseLayout = () => {
  const { styles } = useStyles();

  const location = useLocation();

  useEffect(() => {
    // 监听路由变化，滚动到顶部
    if (location.pathname.includes('/home')) {
      window.scrollTo(0, 0);
    } else {
      window.scrollTo(0, 258);
    }
  }, [location.pathname]);
  return (
    <div>
      <Header />
      <div className={styles.content}>
        <div>
          <Banner />
        </div>
        <div className={styles.container}>
          <div className={styles.left}>
            <LeftMenu />
          </div>
          <div className={styles.center}>
            <Outlet />
          </div>
        </div>
      </div>
      <Footer />
      <FloatButton.BackTop />
    </div>
  );
};

export default BaseLayout;
