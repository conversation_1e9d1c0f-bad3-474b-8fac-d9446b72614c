import useUserStore, { deriveuserStore, userLogout } from '@/store/user';
import {
  FormattedMessage,
  Link,
  useIntl,
  useLocation,
  useParams,
} from '@umijs/max';
import { useCreation, useSafeState } from 'ahooks';
import { Button, Dropdown, Flex } from 'antd';
import { createStyles } from 'antd-style';
import { memo, useEffect } from 'react';
import SelectLang from './components/SelectLang';

const useStyles = createStyles(
  ({ css, token }, props: { isScroll: boolean }) => ({
    header: css`
      position: fixed;
      top: 0;
      z-index: 999;
      width: 100%;
      height: 42px;
      background: rgba(255, 255, 255, ${props.isScroll ? 1 : 0.5});
      padding: 0 14px;
      gap: 14px;
      transition: all 1s;
      img {
        width: 143px;
        height: 24px;
      }
    `,
  }),
);

const Header = () => {
  const [affixed, setAffixed] = useSafeState(false);

  const { isLogin, showVisit } = deriveuserStore;
  const userState = useUserStore();

  const { styles } = useStyles({ isScroll: affixed });
  const params = useParams();
  const location = useLocation();

  const intl = useIntl();

  const logout = intl.formatMessage({ id: 'login.logout' });

  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 0) {
        setAffixed(true);
      } else {
        setAffixed(false);
      }
    });
    return () => {
      window.removeEventListener('scroll', () => {});
    };
  }, []);

  const handleClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      userLogout();
    }
  };

  const showLogin = useCreation(() => {
    return !isLogin && location.pathname !== `/${params.lang}/login`;
  }, [isLogin, params.lang, location.pathname]);
  return (
    <div>
      <Flex justify="space-between" align="center" className={styles.header}>
        <Link to={`/${params.lang}/home`}>
          <img src={require('@/assets/logo.png')} />
        </Link>
        <Flex gap={24}>
          {showVisit && (
            <Flex align='center'>
              <Link to={`/${params.lang}/visit`}>
                <FormattedMessage id="visit.title" />
              </Link>
            </Flex>
          )}
            
          {isLogin ? (
            <Dropdown
              menu={{
                items: [{ key: 'logout', label: logout }],
                onClick: handleClick,
              }}
              trigger={['hover']}
            >
              <Button type="link">{userState.UserName}</Button>
            </Dropdown>
          ) : showLogin ? (
            <Link to={`/${params.lang}/login`}>
              <FormattedMessage id="login.signin" />
            </Link>
          ) : null}

          <SelectLang />
        </Flex>
      </Flex>
    </div>
  );
};

export default memo(Header);
