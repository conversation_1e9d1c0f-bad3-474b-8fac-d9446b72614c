import { useCallback, useEffect, useRef } from 'react';
import { postVisitLog } from '@/services/api';

// 内容类型枚举
export enum ContentType {
  Document = 0,
  HomePage = 1,
  Notice = 2,
}

// 访问日志参数接口
export interface VisitLogParams {
  pageUrl: string;
  contentType: ContentType;
  contentId?: number;
  userAgent?: string;
  referrer?: string;
}

// hooks返回值接口
export interface UseLogVisitReturn {
  logVisit: (params: VisitLogParams) => void; // 改为同步函数，不阻塞调用者
  isLogging: boolean;
}

// 队列管理器，专门设计为非阻塞和容错
class VisitLogQueue {
  private queue: Array<{
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number };
    timestamp: number;
    retryCount: number;
  }> = [];
  private isProcessing = false;
  private idleCallbackId: number | null = null;
  private readonly MAX_QUEUE_SIZE = 1000; // 防止内存泄漏
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAYS = [1000, 3000, 10000]; // 1s, 3s, 10s
  private readonly IDLE_TIMEOUT = 5000; // 5秒超时

  // 添加到队列 - 完全非阻塞
  enqueue(params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number }) {
    try {
      // 防止队列过大
      if (this.queue.length >= this.MAX_QUEUE_SIZE) {
        console.warn('Visit log queue is full, dropping oldest entries');
        this.queue.splice(0, this.queue.length - this.MAX_QUEUE_SIZE + 1);
      }

      this.queue.push({
        params,
        timestamp: Date.now(),
        retryCount: 0,
      });

      // 立即调度处理，但不等待
      this.scheduleIdleProcess();
    } catch (error) {
      // 即使队列操作失败也不影响主流程
      console.warn('Failed to enqueue visit log:', error);
    }
  }

  // 使用 requestIdleCallback 调度处理
  private scheduleIdleProcess() {
    // 如果已经有调度，取消之前的
    if (this.idleCallbackId !== null) {
      this.cancelIdleCallback(this.idleCallbackId);
    }

    // 使用 requestIdleCallback 在浏览器空闲时处理
    if ('requestIdleCallback' in window) {
      this.idleCallbackId = requestIdleCallback(
        (deadline) => this.processInIdle(deadline),
        { timeout: this.IDLE_TIMEOUT }
      );
    } else {
      // 降级方案：使用 setTimeout 模拟
      this.idleCallbackId = setTimeout(() => {
        this.processInIdle({
          timeRemaining: () => 5, // 模拟5ms可用时间
          didTimeout: false
        } as IdleDeadline);
      }, 0) as any;
    }
  }

  // 在空闲时间处理队列 - 支持批量发送
  private processInIdle(deadline: IdleDeadline) {
    try {
      this.idleCallbackId = null;

      if (this.isProcessing || this.queue.length === 0) {
        return;
      }

      this.isProcessing = true;

      // 收集批量处理的项目
      const batchItems: Array<{
        params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number };
        timestamp: number;
        retryCount: number;
      }> = [];

      // 在空闲时间内收集尽可能多的项目进行批量处理
      while (this.queue.length > 0 && (deadline.timeRemaining() > 1 || deadline.didTimeout)) {
        const item = this.queue.shift();
        if (item) {
          batchItems.push(item);
        }

        // 限制批量大小，避免单次请求过大
        if (batchItems.length >= 50) {
          break;
        }
      }

      // 批量处理收集到的项目
      if (batchItems.length > 0) {
        this.processBatchAsync(batchItems);
      }

      this.isProcessing = false;

      // 如果还有未处理的项目，继续调度
      if (this.queue.length > 0) {
        this.scheduleIdleProcess();
      }
    } catch (error) {
      // 处理过程中的任何错误都不应影响主流程
      console.warn('Error in idle processing:', error);
      this.isProcessing = false;

      // 重新调度处理剩余项目
      if (this.queue.length > 0) {
        this.scheduleIdleProcess();
      }
    }
  }

  // 批量异步处理，完全不阻塞
  private processBatchAsync(items: Array<{
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number };
    timestamp: number;
    retryCount: number;
  }>) {
    // 使用 Promise 异步处理，不等待结果
    this.processBatch(items)
      .catch(() => {
        // 批量处理失败时，将项目重新加入队列进行单独重试
        items.forEach(item => {
          if (item.retryCount < this.MAX_RETRIES) {
            const delay = this.RETRY_DELAYS[item.retryCount] || 10000;

            setTimeout(() => {
              try {
                // 重新加入队列进行重试
                this.queue.push({
                  ...item,
                  retryCount: item.retryCount + 1,
                });
                this.scheduleIdleProcess();
              } catch (retryError) {
                console.warn('Failed to schedule retry:', retryError);
              }
            }, delay);
          } else {
            // 达到最大重试次数，记录但不影响主流程
            console.warn('Visit log permanently failed after max retries:', item.params);
          }
        });
      });
  }

  // 批量处理多个项目
  private async processBatch(items: Array<{
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number };
    timestamp: number;
    retryCount: number;
  }>): Promise<void> {
    try {
      // 将项目转换为API期望的格式
      const batchParams = items.map(item => ({
        userAgent: item.params.userAgent,
        referrer: item.params.referrer,
        pageUrl: item.params.pageUrl,
        contentType: item.params.contentType,
        contentId: item.params.contentId,
      }));

      // 批量发送
      await postVisitLog(batchParams);

      // 只在开发环境输出成功日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`✅ Batch visit logs recorded: ${items.length} items`);
      }
    } catch (error) {
      // 记录错误但不抛出，确保不影响主流程
      if (process.env.NODE_ENV === 'development') {
        console.warn(`❌ Batch visit log failed (${items.length} items):`, error);
      }

      // 重新抛出错误以触发重试逻辑
      throw error;
    }
  }

  // 处理单个项目（保留用于兼容性）
  private async processItem(
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number },
    retryCount: number
  ): Promise<void> {
    try {
      // 将单个项目包装成数组发送
      const batchParams = [{
        userAgent: params.userAgent,
        referrer: params.referrer,
        pageUrl: params.pageUrl,
        contentType: params.contentType,
        contentId: params.contentId,
      }];

      await postVisitLog(batchParams);

      // 只在开发环境输出成功日志
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Visit log recorded:', params.pageUrl);
      }
    } catch (error) {
      // 记录错误但不抛出，确保不影响主流程
      if (process.env.NODE_ENV === 'development') {
        console.warn(`❌ Visit log failed (attempt ${retryCount + 1}):`, error);
      }

      // 重新抛出错误以触发重试逻辑
      throw error;
    }
  }

  // 兼容性处理：取消 requestIdleCallback
  private cancelIdleCallback(id: number) {
    if ('cancelIdleCallback' in window) {
      cancelIdleCallback(id);
    } else {
      clearTimeout(id);
    }
  }

  // 获取队列状态（用于调试）
  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      hasScheduledCallback: this.idleCallbackId !== null,
    };
  }

  // 清空队列（用于测试或重置）
  clear() {
    try {
      this.queue.length = 0;
      if (this.idleCallbackId !== null) {
        this.cancelIdleCallback(this.idleCallbackId);
        this.idleCallbackId = null;
      }
      this.isProcessing = false;
    } catch (error) {
      console.warn('Failed to clear queue:', error);
    }
  }
}

// 全局队列实例
const visitLogQueue = new VisitLogQueue();

/**
 * 页面访问量统计 React Hook - 优化版本，不阻塞页面渲染
 * @param autoLog 是否自动记录当前页面访问（默认为false）
 * @param defaultParams 默认参数，用于自动记录时
 * @returns 返回手动记录函数和记录状态
 */
export function useLogVisit(
  autoLog: boolean = false,
  defaultParams?: Partial<VisitLogParams>
): UseLogVisitReturn {
  const hasLoggedRef = useRef(false);
  const isLoggingRef = useRef(false);

  // 获取用户代理字符串
  const getUserAgent = useCallback((): string => {
    return navigator.userAgent || '';
  }, []);

  // 获取来源URL
  const getReferrer = useCallback((): string => {
    return document.referrer || '';
  }, []);

  // 获取当前页面URL
  const getCurrentPageUrl = useCallback((): string => {
    return window.location.pathname + window.location.search;
  }, []);

  // 完全非阻塞且容错的记录访问日志函数
  const logVisit = useCallback((params: VisitLogParams): void => {
    try {
      // 立即返回，不等待网络请求，任何错误都不影响调用者
      const fullParams = {
        pageUrl: params.pageUrl,
        contentType: params.contentType,
        contentId: params.contentId || 0,
        userAgent: params.userAgent || getUserAgent(),
        referrer: params.referrer || getReferrer(),
      };

      // 添加到队列，完全异步处理
      visitLogQueue.enqueue(fullParams);

      // 可选的UI状态反馈（不影响核心功能）
      try {
        isLoggingRef.current = true;

        // 短暂延迟后重置状态
        setTimeout(() => {
          try {
            isLoggingRef.current = false;
          } catch (error) {
            // 即使状态更新失败也不影响
            console.warn('Failed to reset logging state:', error);
          }
        }, 100);
      } catch (error) {
        // 状态管理失败不影响统计功能
        console.warn('Failed to update logging state:', error);
      }
    } catch (error) {
      // 任何错误都不应该影响页面正常功能
      console.warn('Failed to log visit, but page functionality is not affected:', error);
    }
  }, [getUserAgent, getReferrer]);

  // 自动记录当前页面访问 - 完全非阻塞且容错
  useEffect(() => {
    if (autoLog && !hasLoggedRef.current && defaultParams) {
      // 使用 requestIdleCallback 确保不阻塞初始渲染
      let idleCallbackId: number | null = null;

      const scheduleAutoLog = () => {
        if ('requestIdleCallback' in window) {
          idleCallbackId = requestIdleCallback(() => {
            try {
              const autoLogParams: VisitLogParams = {
                pageUrl: getCurrentPageUrl(),
                contentType: defaultParams.contentType || ContentType.HomePage,
                contentId: defaultParams.contentId,
                userAgent: defaultParams.userAgent,
                referrer: defaultParams.referrer,
              };

              logVisit(autoLogParams);
              hasLoggedRef.current = true;
            } catch (error) {
              // 自动记录失败不影响页面功能
              console.warn('Auto log visit failed, but page functionality is not affected:', error);
            }
          }, { timeout: 2000 });
        } else {
          // 降级方案：使用 setTimeout
          idleCallbackId = setTimeout(() => {
            try {
              const autoLogParams: VisitLogParams = {
                pageUrl: getCurrentPageUrl(),
                contentType: defaultParams.contentType || ContentType.HomePage,
                contentId: defaultParams.contentId,
                userAgent: defaultParams.userAgent,
                referrer: defaultParams.referrer,
              };

              logVisit(autoLogParams);
              hasLoggedRef.current = true;
            } catch (error) {
              console.warn('Auto log visit failed, but page functionality is not affected:', error);
            }
          }, 0) as any;
        }
      };

      try {
        scheduleAutoLog();
      } catch (error) {
        console.warn('Failed to schedule auto log, but page functionality is not affected:', error);
      }

      return () => {
        try {
          if (idleCallbackId !== null) {
            if ('cancelIdleCallback' in window) {
              cancelIdleCallback(idleCallbackId);
            } else {
              clearTimeout(idleCallbackId);
            }
          }
        } catch (error) {
          console.warn('Failed to cleanup auto log callback:', error);
        }
      };
    }
  }, [autoLog, defaultParams, logVisit, getCurrentPageUrl]);

  return {
    logVisit,
    isLogging: isLoggingRef.current,
  };
}

// 便捷的自动记录hooks - 非阻塞版本
export function useAutoLogVisit(params: Partial<VisitLogParams>): UseLogVisitReturn {
  return useLogVisit(true, params);
}

// 便捷的文档访问记录hooks - 非阻塞版本
export function useLogDocumentVisit(documentId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Document,
    contentId: documentId,
  });
}

// 便捷的公告访问记录hooks - 非阻塞版本
export function useLogNoticeVisit(noticeId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Notice,
    contentId: noticeId,
  });
}

// 便捷的首页访问记录hooks - 非阻塞版本
export function useLogHomePageVisit(): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.HomePage,
  });
}

// 手动触发的非阻塞记录函数（全局使用）- 完全容错
export const logVisitGlobal = (params: VisitLogParams): void => {
  try {
    const fullParams = {
      pageUrl: params.pageUrl,
      contentType: params.contentType,
      contentId: params.contentId || 0,
      userAgent: params.userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : '') || '',
      referrer: params.referrer || (typeof document !== 'undefined' ? document.referrer : '') || '',
    };

    visitLogQueue.enqueue(fullParams);
  } catch (error) {
    // 全局函数的任何错误都不应该影响调用者
    console.warn('Global visit log failed, but application functionality is not affected:', error);
  }
};

// 获取队列状态的调试函数
export const getVisitLogQueueStatus = () => {
  try {
    return visitLogQueue.getQueueStatus();
  } catch (error) {
    console.warn('Failed to get queue status:', error);
    return { queueLength: 0, isProcessing: false, hasScheduledCallback: false };
  }
};

// 清空队列的函数（用于测试或重置）
export const clearVisitLogQueue = () => {
  try {
    visitLogQueue.clear();
  } catch (error) {
    console.warn('Failed to clear visit log queue:', error);
  }
};

export default useLogVisit;