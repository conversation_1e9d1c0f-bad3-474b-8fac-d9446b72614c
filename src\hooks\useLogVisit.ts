import { useCallback, useEffect, useRef } from 'react';
import { postVisitLog } from '@/services/api';

// 内容类型枚举
export enum ContentType {
  Document = 0,
  HomePage = 1,
  Notice = 2,
}

// 访问日志参数接口
export interface VisitLogParams {
  pageUrl: string;
  contentType: ContentType;
  contentId?: number;
  userAgent?: string;
  referrer?: string;
}

// hooks返回值接口
export interface UseLogVisitReturn {
  logVisit: (params: VisitLogParams) => void; // 改为同步函数，不阻塞调用者
  isLogging: boolean;
}

// 队列管理器，用于批量处理和延迟执行
class VisitLogQueue {
  private queue: Array<{
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number };
    timestamp: number;
  }> = [];
  private isProcessing = false;
  private batchTimeout: NodeJS.Timeout | null = null;
  private readonly BATCH_DELAY = 100; // 100ms延迟批量处理
  private readonly MAX_RETRIES = 3;

  // 添加到队列
  enqueue(params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number }) {
    this.queue.push({
      params,
      timestamp: Date.now(),
    });

    // 延迟处理，避免阻塞渲染
    this.scheduleProcess();
  }

  // 调度处理
  private scheduleProcess() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(() => {
      this.processQueue();
    }, this.BATCH_DELAY);
  }

  // 处理队列
  private async processQueue() {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    // 使用requestIdleCallback在浏览器空闲时处理
    const processInIdle = () => {
      if ('requestIdleCallback' in window) {
        requestIdleCallback(() => this.processBatch(), { timeout: 5000 });
      } else {
        // 降级方案：使用setTimeout
        setTimeout(() => this.processBatch(), 0);
      }
    };

    processInIdle();
  }

  // 批量处理
  private async processBatch() {
    const batch = this.queue.splice(0); // 取出所有待处理项

    for (const item of batch) {
      await this.processItem(item.params, 0);
    }

    this.isProcessing = false;

    // 如果处理期间又有新的项目加入，继续处理
    if (this.queue.length > 0) {
      this.scheduleProcess();
    }
  }

  // 处理单个项目，带重试机制
  private async processItem(
    params: Required<Omit<VisitLogParams, 'contentId'>> & { contentId: number },
    retryCount: number
  ) {
    try {
      await postVisitLog(params);
      console.log('Visit log recorded successfully:', params);
    } catch (error) {
      console.error(`Failed to record visit log (attempt ${retryCount + 1}):`, error);

      // 重试机制
      if (retryCount < this.MAX_RETRIES) {
        const delay = Math.pow(2, retryCount) * 1000; // 指数退避
        setTimeout(() => {
          this.processItem(params, retryCount + 1);
        }, delay);
      } else {
        console.error('Max retries reached for visit log:', params);
      }
    }
  }
}

// 全局队列实例
const visitLogQueue = new VisitLogQueue();

/**
 * 页面访问量统计 React Hook - 优化版本，不阻塞页面渲染
 * @param autoLog 是否自动记录当前页面访问（默认为false）
 * @param defaultParams 默认参数，用于自动记录时
 * @returns 返回手动记录函数和记录状态
 */
export function useLogVisit(
  autoLog: boolean = false,
  defaultParams?: Partial<VisitLogParams>
): UseLogVisitReturn {
  const hasLoggedRef = useRef(false);
  const isLoggingRef = useRef(false);

  // 获取用户代理字符串
  const getUserAgent = useCallback((): string => {
    return navigator.userAgent || '';
  }, []);

  // 获取来源URL
  const getReferrer = useCallback((): string => {
    return document.referrer || '';
  }, []);

  // 获取当前页面URL
  const getCurrentPageUrl = useCallback((): string => {
    return window.location.pathname + window.location.search;
  }, []);

  // 非阻塞的记录访问日志函数
  const logVisit = useCallback((params: VisitLogParams): void => {
    // 立即返回，不等待网络请求
    const fullParams = {
      pageUrl: params.pageUrl,
      contentType: params.contentType,
      contentId: params.contentId || 0,
      userAgent: params.userAgent || getUserAgent(),
      referrer: params.referrer || getReferrer(),
    };

    // 添加到队列，异步处理
    visitLogQueue.enqueue(fullParams);

    // 更新状态（可选，用于UI反馈）
    isLoggingRef.current = true;

    // 短暂延迟后重置状态
    setTimeout(() => {
      isLoggingRef.current = false;
    }, 200);
  }, [getUserAgent, getReferrer]);

  // 自动记录当前页面访问 - 使用非阻塞方式
  useEffect(() => {
    if (autoLog && !hasLoggedRef.current && defaultParams) {
      // 延迟执行，确保不阻塞初始渲染
      const timeoutId = setTimeout(() => {
        const autoLogParams: VisitLogParams = {
          pageUrl: getCurrentPageUrl(),
          contentType: defaultParams.contentType || ContentType.HomePage,
          contentId: defaultParams.contentId,
          userAgent: defaultParams.userAgent,
          referrer: defaultParams.referrer,
        };

        logVisit(autoLogParams);
        hasLoggedRef.current = true;
      }, 0); // 下一个事件循环执行

      return () => clearTimeout(timeoutId);
    }
  }, [autoLog, defaultParams, logVisit, getCurrentPageUrl]);

  return {
    logVisit,
    isLogging: isLoggingRef.current,
  };
}

// 便捷的自动记录hooks - 非阻塞版本
export function useAutoLogVisit(params: Partial<VisitLogParams>): UseLogVisitReturn {
  return useLogVisit(true, params);
}

// 便捷的文档访问记录hooks - 非阻塞版本
export function useLogDocumentVisit(documentId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Document,
    contentId: documentId,
  });
}

// 便捷的公告访问记录hooks - 非阻塞版本
export function useLogNoticeVisit(noticeId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Notice,
    contentId: noticeId,
  });
}

// 便捷的首页访问记录hooks - 非阻塞版本
export function useLogHomePageVisit(): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.HomePage,
  });
}

// 手动触发的非阻塞记录函数（全局使用）
export const logVisitGlobal = (params: VisitLogParams): void => {
  const fullParams = {
    pageUrl: params.pageUrl,
    contentType: params.contentType,
    contentId: params.contentId || 0,
    userAgent: params.userAgent || navigator.userAgent || '',
    referrer: params.referrer || document.referrer || '',
  };

  visitLogQueue.enqueue(fullParams);
};

export default useLogVisit;