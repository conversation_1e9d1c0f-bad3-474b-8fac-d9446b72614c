import { useCallback, useEffect, useRef } from 'react';
import { postVisitLog } from '@/services/api';

// 内容类型枚举
export enum ContentType {
  Document = 0,
  HomePage = 1,
  Notice = 2,
}

// 访问日志参数接口
export interface VisitLogParams {
  pageUrl: string;
  contentType: ContentType;
  contentId?: number;
  userAgent?: string;
  referrer?: string;
}

// hooks返回值接口
export interface UseLogVisitReturn {
  logVisit: (params: VisitLogParams) => Promise<void>;
  isLogging: boolean;
}

/**
 * 页面访问量统计 React Hook
 * @param autoLog 是否自动记录当前页面访问（默认为false）
 * @param defaultParams 默认参数，用于自动记录时
 * @returns 返回手动记录函数和记录状态
 */
export function useLogVisit(
  autoLog: boolean = false,
  defaultParams?: Partial<VisitLogParams>
): UseLogVisitReturn {
  const isLoggingRef = useRef(false);
  const hasLoggedRef = useRef(false);

  // 获取用户代理字符串
  const getUserAgent = useCallback((): string => {
    return navigator.userAgent || '';
  }, []);

  // 获取来源URL
  const getReferrer = useCallback((): string => {
    return document.referrer || '';
  }, []);

  // 获取当前页面URL
  const getCurrentPageUrl = useCallback((): string => {
    return window.location.pathname + window.location.search;
  }, []);

  // 记录访问日志的核心函数
  const logVisit = useCallback(async (params: VisitLogParams): Promise<void> => {
    if (isLoggingRef.current) {
      console.warn('Visit log is already being recorded');
      return;
    }

    try {
      isLoggingRef.current = true;

      // 构建完整的参数
      const fullParams = {
        pageUrl: params.pageUrl,
        contentType: params.contentType,
        contentId: params.contentId || 0,
        userAgent: params.userAgent || getUserAgent(),
        referrer: params.referrer || getReferrer(),
      };

      // 调用API记录访问日志
      await postVisitLog(fullParams);

      console.log('Visit log recorded successfully:', fullParams);
    } catch (error) {
      console.error('Failed to record visit log:', error);
      // 这里可以根据需要添加错误处理逻辑，比如重试机制
    } finally {
      isLoggingRef.current = false;
    }
  }, [getUserAgent, getReferrer]);

  // 自动记录当前页面访问
  useEffect(() => {
    if (autoLog && !hasLoggedRef.current && defaultParams) {
      const autoLogParams: VisitLogParams = {
        pageUrl: getCurrentPageUrl(),
        contentType: defaultParams.contentType || ContentType.HomePage,
        contentId: defaultParams.contentId,
        userAgent: defaultParams.userAgent,
        referrer: defaultParams.referrer,
      };

      logVisit(autoLogParams);
      hasLoggedRef.current = true;
    }
  }, [autoLog, defaultParams, logVisit, getCurrentPageUrl]);

  return {
    logVisit,
    isLogging: isLoggingRef.current,
  };
}

// 便捷的自动记录hooks
export function useAutoLogVisit(params: Partial<VisitLogParams>): UseLogVisitReturn {
  return useLogVisit(true, params);
}

// 便捷的文档访问记录hooks
export function useLogDocumentVisit(documentId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Document,
    contentId: documentId,
  });
}

// 便捷的公告访问记录hooks
export function useLogNoticeVisit(noticeId: number): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.Notice,
    contentId: noticeId,
  });
}

// 便捷的首页访问记录hooks
export function useLogHomePageVisit(): UseLogVisitReturn {
  return useLogVisit(true, {
    contentType: ContentType.HomePage,
  });
}

export default useLogVisit;