import React, { useState, useCallback, useEffect } from 'react';
import { 
  useLogVisit, 
  logVisitGlobal, 
  getVisitLogQueueStatus,
  clearVisitLogQueue,
  ContentType 
} from './useLogVisit';

// 展示 requestIdleCallback 和容错特性的示例
export const IdleCallbackExample: React.FC = () => {
  const [queueStatus, setQueueStatus] = useState({ queueLength: 0, isProcessing: false, hasScheduledCallback: false });
  const [performanceMetrics, setPerformanceMetrics] = useState<number[]>([]);
  const [errorCount, setErrorCount] = useState(0);
  const { logVisit } = useLogVisit();

  // 定期更新队列状态
  useEffect(() => {
    const interval = setInterval(() => {
      try {
        const status = getVisitLogQueueStatus();
        setQueueStatus(status);
      } catch (error) {
        console.warn('Failed to get queue status:', error);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  // 测试大量快速操作
  const handleStressTest = useCallback(() => {
    const startTime = performance.now();
    
    try {
      // 快速生成大量统计请求
      for (let i = 0; i < 100; i++) {
        logVisit({
          pageUrl: `/stress-test-${i}`,
          contentType: ContentType.Document,
          contentId: i,
        });
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setPerformanceMetrics(prev => [...prev.slice(-9), duration]);
      
      console.log(`✅ 100个统计请求入队耗时: ${duration.toFixed(2)}ms`);
    } catch (error) {
      setErrorCount(prev => prev + 1);
      console.warn('Stress test failed:', error);
    }
  }, [logVisit]);

  // 测试全局函数
  const handleGlobalTest = useCallback(() => {
    const startTime = performance.now();
    
    try {
      for (let i = 0; i < 50; i++) {
        logVisitGlobal({
          pageUrl: `/global-test-${i}`,
          contentType: ContentType.HomePage,
        });
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setPerformanceMetrics(prev => [...prev.slice(-9), duration]);
      
      console.log(`✅ 50个全局统计请求入队耗时: ${duration.toFixed(2)}ms`);
    } catch (error) {
      setErrorCount(prev => prev + 1);
      console.warn('Global test failed:', error);
    }
  }, []);

  // 模拟网络错误测试
  const handleErrorTest = useCallback(() => {
    try {
      // 发送一些可能失败的请求
      for (let i = 0; i < 10; i++) {
        logVisit({
          pageUrl: `/error-test-${i}`,
          contentType: ContentType.Notice,
          contentId: i,
        });
      }
      
      console.log('✅ 错误测试请求已发送，即使失败也不会影响页面功能');
    } catch (error) {
      setErrorCount(prev => prev + 1);
      console.warn('Error test failed:', error);
    }
  }, [logVisit]);

  // 清空队列
  const handleClearQueue = useCallback(() => {
    try {
      clearVisitLogQueue();
      console.log('✅ 队列已清空');
    } catch (error) {
      setErrorCount(prev => prev + 1);
      console.warn('Failed to clear queue:', error);
    }
  }, []);

  const averageTime = performanceMetrics.length > 0 
    ? (performanceMetrics.reduce((a, b) => a + b, 0) / performanceMetrics.length).toFixed(2)
    : '0';

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>🚀 requestIdleCallback 和容错特性演示</h3>
      
      {/* 队列状态监控 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: '#f0f8ff',
        border: '1px solid #87ceeb',
        borderRadius: '4px'
      }}>
        <h4>📊 实时队列状态</h4>
        <p>队列长度: <strong>{queueStatus.queueLength}</strong></p>
        <p>正在处理: <strong>{queueStatus.isProcessing ? '是' : '否'}</strong></p>
        <p>已调度回调: <strong>{queueStatus.hasScheduledCallback ? '是' : '否'}</strong></p>
        <p>错误次数: <strong>{errorCount}</strong></p>
      </div>

      {/* 性能指标 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '15px', 
        backgroundColor: '#f6ffed',
        border: '1px solid #b7eb8f',
        borderRadius: '4px'
      }}>
        <h4>⚡ 性能指标</h4>
        <p>平均响应时间: <strong>{averageTime}ms</strong></p>
        <p>最近响应时间: {performanceMetrics.slice(-3).map(t => t.toFixed(2)).join('ms, ')}ms</p>
        <p>支持的浏览器特性: <strong>{typeof requestIdleCallback !== 'undefined' ? 'requestIdleCallback' : 'setTimeout降级'}</strong></p>
      </div>

      {/* 测试按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleStressTest}
          style={{ 
            padding: '10px 15px', 
            marginRight: '10px',
            marginBottom: '10px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          压力测试 (100个请求)
        </button>
        
        <button 
          onClick={handleGlobalTest}
          style={{ 
            padding: '10px 15px',
            marginRight: '10px',
            marginBottom: '10px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          全局函数测试 (50个请求)
        </button>
        
        <button 
          onClick={handleErrorTest}
          style={{ 
            padding: '10px 15px',
            marginRight: '10px',
            marginBottom: '10px',
            backgroundColor: '#fa8c16',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          容错测试
        </button>
        
        <button 
          onClick={handleClearQueue}
          style={{ 
            padding: '10px 15px',
            marginBottom: '10px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          清空队列
        </button>
      </div>

      {/* 特性说明 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#fff7e6',
        border: '1px solid #ffd591',
        borderRadius: '4px'
      }}>
        <h4>🎯 requestIdleCallback 特性</h4>
        <ul>
          <li>✅ 统计请求在浏览器空闲时处理，不阻塞主线程</li>
          <li>✅ 自动降级到 setTimeout，确保兼容性</li>
          <li>✅ 智能队列管理，防止内存泄漏</li>
          <li>✅ 完全容错设计，统计失败不影响页面功能</li>
          <li>✅ 自动重试机制，确保数据可靠性</li>
          <li>✅ 实时状态监控，便于调试和优化</li>
        </ul>
      </div>

      {/* 使用建议 */}
      <div style={{ 
        marginTop: '15px',
        padding: '15px', 
        backgroundColor: '#f0f2f5',
        border: '1px solid #d9d9d9',
        borderRadius: '4px'
      }}>
        <h4>💡 使用建议</h4>
        <ul>
          <li>在高频操作场景中使用，如滚动、点击、输入等</li>
          <li>适合移动端和低性能设备</li>
          <li>网络不稳定环境下的可靠统计</li>
          <li>不需要等待统计结果的场景</li>
        </ul>
      </div>
    </div>
  );
};

// 模拟真实使用场景的组件
export const RealWorldExample: React.FC = () => {
  const [scrollCount, setScrollCount] = useState(0);
  const { logVisit } = useLogVisit();

  // 模拟滚动统计
  useEffect(() => {
    const handleScroll = () => {
      try {
        setScrollCount(prev => prev + 1);
        
        // 高频滚动统计，完全不阻塞滚动性能
        logVisit({
          pageUrl: window.location.pathname,
          contentType: ContentType.HomePage,
          contentId: Math.floor(window.scrollY / 100), // 每100px一个统计点
        });
      } catch (error) {
        console.warn('Scroll tracking failed:', error);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [logVisit]);

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>🌍 真实场景示例</h3>
      <p>滚动次数: <strong>{scrollCount}</strong></p>
      <p>每次滚动都会触发统计，但完全不影响滚动性能</p>
      
      {/* 创建可滚动内容 */}
      <div style={{ height: '200px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px' }}>
        {Array.from({ length: 100 }, (_, i) => (
          <div key={i} style={{ padding: '10px 0', borderBottom: '1px solid #eee' }}>
            滚动内容行 {i + 1} - 滚动时会触发统计但不影响性能
          </div>
        ))}
      </div>
      
      <p style={{ marginTop: '10px', color: '#666' }}>
        💡 尝试滚动上面的内容，观察统计如何在不影响滚动流畅度的情况下工作
      </p>
    </div>
  );
};

export default IdleCallbackExample;
