# useLogVisit Hook 使用说明

这是一个用于统计页面访问量的 React Hook，支持自动和手动记录页面访问日志。

## 功能特性

- ✅ 支持手动和自动记录访问日志
- ✅ 自动获取用户代理字符串和来源URL
- ✅ 支持不同内容类型（文档、首页、公告）
- ✅ 防重复记录机制
- ✅ 错误处理和状态管理
- ✅ 提供多个便捷的专用hooks

## 参数说明

### VisitLogParams 接口

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| pageUrl | string | ✅ | 用户访问路径，可以为相对路径 |
| contentType | ContentType | ✅ | 内容类型：Document=0, HomePage=1, Notice=2 |
| contentId | number | ❌ | 内容ID，当访问文档时为DocumentId |
| userAgent | string | ❌ | 用户浏览器/设备的User-Agent字符串，不传则自动获取 |
| referrer | string | ❌ | 来源URL，不传则自动获取 |

### ContentType 枚举

```typescript
enum ContentType {
  Document = 0,  // 文档
  HomePage = 1,  // 首页
  Notice = 2,    // 公告
}
```

## 基本用法

### 1. 手动记录访问日志

```typescript
import { useLogVisit, ContentType } from '@/hooks/useLogVisit';

const MyComponent = () => {
  const { logVisit, isLogging } = useLogVisit();

  const handleLogVisit = async () => {
    await logVisit({
      pageUrl: '/documents/example',
      contentType: ContentType.Document,
      contentId: 123,
    });
  };

  return (
    <button onClick={handleLogVisit} disabled={isLogging}>
      {isLogging ? '记录中...' : '记录访问'}
    </button>
  );
};
```

### 2. 自动记录当前页面访问

```typescript
import { useAutoLogVisit, ContentType } from '@/hooks/useLogVisit';

const HomePage = () => {
  // 组件加载时自动记录访问
  const { isLogging } = useAutoLogVisit({
    contentType: ContentType.HomePage,
  });

  return (
    <div>
      <h1>首页</h1>
      {isLogging && <p>正在记录访问...</p>}
    </div>
  );
};
```

## 便捷Hooks

### useLogDocumentVisit - 文档访问记录

```typescript
import { useLogDocumentVisit } from '@/hooks/useLogVisit';

const DocumentPage = ({ documentId }: { documentId: number }) => {
  const { isLogging } = useLogDocumentVisit(documentId);
  
  return (
    <div>
      <h1>文档页面</h1>
      {isLogging && <p>正在记录文档访问...</p>}
    </div>
  );
};
```

### useLogNoticeVisit - 公告访问记录

```typescript
import { useLogNoticeVisit } from '@/hooks/useLogVisit';

const NoticePage = ({ noticeId }: { noticeId: number }) => {
  const { isLogging } = useLogNoticeVisit(noticeId);
  
  return (
    <div>
      <h1>公告页面</h1>
      {isLogging && <p>正在记录公告访问...</p>}
    </div>
  );
};
```

### useLogHomePageVisit - 首页访问记录

```typescript
import { useLogHomePageVisit } from '@/hooks/useLogVisit';

const HomePage = () => {
  const { isLogging } = useLogHomePageVisit();
  
  return (
    <div>
      <h1>欢迎来到首页</h1>
      {isLogging && <p>正在记录首页访问...</p>}
    </div>
  );
};
```

## 高级用法

### 自定义参数记录

```typescript
const { logVisit } = useLogVisit();

await logVisit({
  pageUrl: '/custom-page',
  contentType: ContentType.Document,
  contentId: 456,
  userAgent: 'Custom User Agent String',
  referrer: 'https://example.com/previous-page',
});
```

### 在路由组件中使用

```typescript
const RouteComponent = () => {
  const { logVisit } = useLogVisit();

  useEffect(() => {
    const contentType = getContentTypeFromPath(location.pathname);
    const contentId = extractIdFromPath(location.pathname);
    
    logVisit({
      pageUrl: location.pathname + location.search,
      contentType,
      contentId,
    });
  }, [location.pathname, logVisit]);

  return <div>页面内容</div>;
};
```

## 注意事项

1. **防重复记录**: Hook内部有防重复记录机制，同一个组件实例不会重复记录
2. **错误处理**: 网络错误会被捕获并在控制台输出，不会影响页面正常运行
3. **自动获取**: userAgent和referrer如果不传会自动从浏览器获取
4. **性能优化**: 使用useRef避免不必要的重新渲染

## API 参考

### useLogVisit(autoLog?, defaultParams?)

主要的Hook函数

**参数:**
- `autoLog`: boolean - 是否自动记录（默认false）
- `defaultParams`: Partial<VisitLogParams> - 默认参数

**返回值:**
- `logVisit`: (params: VisitLogParams) => Promise<void> - 手动记录函数
- `isLogging`: boolean - 是否正在记录中

### 便捷Hooks

- `useAutoLogVisit(params)` - 自动记录访问
- `useLogDocumentVisit(documentId)` - 文档访问记录
- `useLogNoticeVisit(noticeId)` - 公告访问记录
- `useLogHomePageVisit()` - 首页访问记录
