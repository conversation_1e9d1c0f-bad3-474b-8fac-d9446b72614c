# useLogVisit Hook 使用说明

这是一个用于统计页面访问量的 React Hook，**专门优化为不阻塞页面渲染**，支持自动和手动记录页面访问日志。

## 🚀 性能优化特性

- ✅ **非阻塞设计**: 统计请求不会阻塞页面渲染和用户交互
- ✅ **队列批处理**: 使用智能队列管理，批量处理统计请求
- ✅ **空闲时处理**: 利用 `requestIdleCallback` 在浏览器空闲时处理
- ✅ **自动重试**: 内置指数退避重试机制，确保数据可靠性
- ✅ **延迟执行**: 自动记录延迟到下一个事件循环，不影响初始渲染

## 功能特性

- ✅ 支持手动和自动记录访问日志
- ✅ 自动获取用户代理字符串和来源URL
- ✅ 支持不同内容类型（文档、首页、公告）
- ✅ 防重复记录机制
- ✅ 错误处理和状态管理
- ✅ 提供多个便捷的专用hooks
- ✅ 全局函数支持，可在非React组件中使用

## 参数说明

### VisitLogParams 接口

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| pageUrl | string | ✅ | 用户访问路径，可以为相对路径 |
| contentType | ContentType | ✅ | 内容类型：Document=0, HomePage=1, Notice=2 |
| contentId | number | ❌ | 内容ID，当访问文档时为DocumentId |
| userAgent | string | ❌ | 用户浏览器/设备的User-Agent字符串，不传则自动获取 |
| referrer | string | ❌ | 来源URL，不传则自动获取 |

### ContentType 枚举

```typescript
enum ContentType {
  Document = 0,  // 文档
  HomePage = 1,  // 首页
  Notice = 2,    // 公告
}
```

## 基本用法

### 1. 手动记录访问日志 - 非阻塞版本

```typescript
import { useLogVisit, ContentType } from '@/hooks/useLogVisit';

const MyComponent = () => {
  const { logVisit, isLogging } = useLogVisit();

  const handleLogVisit = () => {
    // ✅ 不使用 await，立即返回，不阻塞UI
    logVisit({
      pageUrl: '/documents/example',
      contentType: ContentType.Document,
      contentId: 123,
    });
    // 函数立即返回，统计请求在后台队列中异步处理
  };

  return (
    <button onClick={handleLogVisit} disabled={isLogging}>
      {isLogging ? '记录中...' : '记录访问'}
    </button>
  );
};
```

### 1.1 全局函数使用（推荐用于非React组件）

```typescript
import { logVisitGlobal, ContentType } from '@/hooks/useLogVisit';

// 可在任何地方使用，不需要hooks
const handleClick = () => {
  logVisitGlobal({
    pageUrl: '/any-page',
    contentType: ContentType.Document,
    contentId: 123,
  });
  // 立即返回，不阻塞执行
};
```

### 2. 自动记录当前页面访问

```typescript
import { useAutoLogVisit, ContentType } from '@/hooks/useLogVisit';

const HomePage = () => {
  // 组件加载时自动记录访问
  const { isLogging } = useAutoLogVisit({
    contentType: ContentType.HomePage,
  });

  return (
    <div>
      <h1>首页</h1>
      {isLogging && <p>正在记录访问...</p>}
    </div>
  );
};
```

## 便捷Hooks

### useLogDocumentVisit - 文档访问记录

```typescript
import { useLogDocumentVisit } from '@/hooks/useLogVisit';

const DocumentPage = ({ documentId }: { documentId: number }) => {
  const { isLogging } = useLogDocumentVisit(documentId);
  
  return (
    <div>
      <h1>文档页面</h1>
      {isLogging && <p>正在记录文档访问...</p>}
    </div>
  );
};
```

### useLogNoticeVisit - 公告访问记录

```typescript
import { useLogNoticeVisit } from '@/hooks/useLogVisit';

const NoticePage = ({ noticeId }: { noticeId: number }) => {
  const { isLogging } = useLogNoticeVisit(noticeId);
  
  return (
    <div>
      <h1>公告页面</h1>
      {isLogging && <p>正在记录公告访问...</p>}
    </div>
  );
};
```

### useLogHomePageVisit - 首页访问记录

```typescript
import { useLogHomePageVisit } from '@/hooks/useLogVisit';

const HomePage = () => {
  const { isLogging } = useLogHomePageVisit();
  
  return (
    <div>
      <h1>欢迎来到首页</h1>
      {isLogging && <p>正在记录首页访问...</p>}
    </div>
  );
};
```

## 高级用法

### 自定义参数记录

```typescript
const { logVisit } = useLogVisit();

await logVisit({
  pageUrl: '/custom-page',
  contentType: ContentType.Document,
  contentId: 456,
  userAgent: 'Custom User Agent String',
  referrer: 'https://example.com/previous-page',
});
```

### 在路由组件中使用

```typescript
const RouteComponent = () => {
  const { logVisit } = useLogVisit();

  useEffect(() => {
    const contentType = getContentTypeFromPath(location.pathname);
    const contentId = extractIdFromPath(location.pathname);
    
    logVisit({
      pageUrl: location.pathname + location.search,
      contentType,
      contentId,
    });
  }, [location.pathname, logVisit]);

  return <div>页面内容</div>;
};
```

## 注意事项

1. **非阻塞设计**: 所有统计请求都是非阻塞的，不会影响页面渲染和用户交互
2. **防重复记录**: Hook内部有防重复记录机制，同一个组件实例不会重复记录
3. **错误处理**: 网络错误会被捕获并自动重试，不会影响页面正常运行
4. **自动获取**: userAgent和referrer如果不传会自动从浏览器获取
5. **性能优化**: 使用队列批处理和空闲时间处理，最大化性能
6. **内存友好**: 队列有合理的大小限制，避免内存泄漏

## 🔧 性能优化原理

1. **队列管理**: 统计请求被添加到队列中，批量处理
2. **延迟执行**: 使用 `setTimeout(0)` 和 `requestIdleCallback` 延迟到空闲时间
3. **指数退避**: 失败请求使用指数退避算法重试
4. **同步返回**: `logVisit` 函数立即返回，不等待网络请求

## API 参考

### useLogVisit(autoLog?, defaultParams?)

主要的Hook函数

**参数:**

- `autoLog`: boolean - 是否自动记录（默认false）
- `defaultParams`: Partial<VisitLogParams> - 默认参数

**返回值:**

- `logVisit`: (params: VisitLogParams) => void - 手动记录函数（非阻塞）
- `isLogging`: boolean - 是否正在记录中

### 便捷Hooks函数

- `useAutoLogVisit(params)` - 自动记录访问
- `useLogDocumentVisit(documentId)` - 文档访问记录
- `useLogNoticeVisit(noticeId)` - 公告访问记录
- `useLogHomePageVisit()` - 首页访问记录

### 全局函数

- `logVisitGlobal(params)` - 全局记录函数，可在任何地方使用
