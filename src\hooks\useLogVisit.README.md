# useLogVisit Hook 使用说明

这是一个用于统计页面访问量的 React Hook，**专门优化为不阻塞页面渲染**，支持自动和手动记录页面访问日志。

## 🚀 性能优化特性

- ✅ **完全非阻塞**: 统计请求绝不阻塞页面渲染和用户交互
- ✅ **requestIdleCallback**: 利用浏览器空闲时间处理，自动降级兼容
- ✅ **智能批量处理**: 自动将多个请求合并为批量发送，减少网络开销
- ✅ **智能队列**: 防内存泄漏的队列管理，支持高频操作
- ✅ **容错设计**: 统计失败完全不影响页面正常功能
- ✅ **自动重试**: 智能重试机制，确保数据可靠性
- ✅ **实时监控**: 提供队列状态监控和调试工具

## 功能特性

- ✅ 支持手动和自动记录访问日志
- ✅ 自动获取用户代理字符串和来源URL
- ✅ 支持不同内容类型（文档、首页、公告）
- ✅ 防重复记录机制
- ✅ 错误处理和状态管理
- ✅ 提供多个便捷的专用hooks
- ✅ 全局函数支持，可在非React组件中使用

## 参数说明

### VisitLogParams 接口

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| pageUrl | string | ✅ | 用户访问路径，可以为相对路径 |
| contentType | ContentType | ✅ | 内容类型：Document=0, HomePage=1, Notice=2 |
| contentId | number | ❌ | 内容ID，当访问文档时为DocumentId |
| userAgent | string | ❌ | 用户浏览器/设备的User-Agent字符串，不传则自动获取 |
| referrer | string | ❌ | 来源URL，不传则自动获取 |

### ContentType 枚举

```typescript
enum ContentType {
  Document = 0,  // 文档
  HomePage = 1,  // 首页
  Notice = 2,    // 公告
}
```

## 基本用法

### 1. 手动记录访问日志 - 非阻塞版本

```typescript
import { useLogVisit, ContentType } from '@/hooks/useLogVisit';

const MyComponent = () => {
  const { logVisit, isLogging } = useLogVisit();

  const handleLogVisit = () => {
    // ✅ 不使用 await，立即返回，不阻塞UI
    logVisit({
      pageUrl: '/documents/example',
      contentType: ContentType.Document,
      contentId: 123,
    });
    // 函数立即返回，统计请求在后台队列中异步处理
  };

  return (
    <button onClick={handleLogVisit} disabled={isLogging}>
      {isLogging ? '记录中...' : '记录访问'}
    </button>
  );
};
```

### 1.1 全局函数使用（推荐用于非React组件）

```typescript
import { logVisitGlobal, ContentType } from '@/hooks/useLogVisit';

// 可在任何地方使用，不需要hooks
const handleClick = () => {
  logVisitGlobal({
    pageUrl: '/any-page',
    contentType: ContentType.Document,
    contentId: 123,
  });
  // 立即返回，不阻塞执行
};
```

### 2. 自动记录当前页面访问

```typescript
import { useAutoLogVisit, ContentType } from '@/hooks/useLogVisit';

const HomePage = () => {
  // 组件加载时自动记录访问
  const { isLogging } = useAutoLogVisit({
    contentType: ContentType.HomePage,
  });

  return (
    <div>
      <h1>首页</h1>
      {isLogging && <p>正在记录访问...</p>}
    </div>
  );
};
```

## 便捷Hooks

### useLogDocumentVisit - 文档访问记录

```typescript
import { useLogDocumentVisit } from '@/hooks/useLogVisit';

const DocumentPage = ({ documentId }: { documentId: number }) => {
  const { isLogging } = useLogDocumentVisit(documentId);
  
  return (
    <div>
      <h1>文档页面</h1>
      {isLogging && <p>正在记录文档访问...</p>}
    </div>
  );
};
```

### useLogNoticeVisit - 公告访问记录

```typescript
import { useLogNoticeVisit } from '@/hooks/useLogVisit';

const NoticePage = ({ noticeId }: { noticeId: number }) => {
  const { isLogging } = useLogNoticeVisit(noticeId);
  
  return (
    <div>
      <h1>公告页面</h1>
      {isLogging && <p>正在记录公告访问...</p>}
    </div>
  );
};
```

### useLogHomePageVisit - 首页访问记录

```typescript
import { useLogHomePageVisit } from '@/hooks/useLogVisit';

const HomePage = () => {
  const { isLogging } = useLogHomePageVisit();
  
  return (
    <div>
      <h1>欢迎来到首页</h1>
      {isLogging && <p>正在记录首页访问...</p>}
    </div>
  );
};
```

## 高级用法

### 自定义参数记录

```typescript
const { logVisit } = useLogVisit();

await logVisit({
  pageUrl: '/custom-page',
  contentType: ContentType.Document,
  contentId: 456,
  userAgent: 'Custom User Agent String',
  referrer: 'https://example.com/previous-page',
});
```

### 在路由组件中使用

```typescript
const RouteComponent = () => {
  const { logVisit } = useLogVisit();

  useEffect(() => {
    const contentType = getContentTypeFromPath(location.pathname);
    const contentId = extractIdFromPath(location.pathname);
    
    logVisit({
      pageUrl: location.pathname + location.search,
      contentType,
      contentId,
    });
  }, [location.pathname, logVisit]);

  return <div>页面内容</div>;
};
```

## 注意事项

1. **非阻塞设计**: 所有统计请求都是非阻塞的，不会影响页面渲染和用户交互
2. **防重复记录**: Hook内部有防重复记录机制，同一个组件实例不会重复记录
3. **错误处理**: 网络错误会被捕获并自动重试，不会影响页面正常运行
4. **自动获取**: userAgent和referrer如果不传会自动从浏览器获取
5. **性能优化**: 使用队列批处理和空闲时间处理，最大化性能
6. **内存友好**: 队列有合理的大小限制，避免内存泄漏

## 🔧 核心技术原理

### requestIdleCallback 优化

利用浏览器的 `requestIdleCallback` API 在空闲时间处理统计请求：

```typescript
// 在浏览器空闲时处理队列
requestIdleCallback((deadline) => {
  while (queue.length > 0 && deadline.timeRemaining() > 1) {
    processItem(queue.shift());
  }
}, { timeout: 5000 });
```

**优势**:
- 不与页面渲染、动画、用户交互竞争CPU时间
- 自动适应设备性能，低性能设备自动降低处理频率
- 兼容性降级到 `setTimeout`，确保所有浏览器支持

### 完全容错设计

每个环节都有错误处理，确保统计失败不影响页面功能：

```typescript
try {
  // 统计逻辑
  logVisit(params);
} catch (error) {
  // 任何错误都不影响页面功能
  console.warn('Statistics failed, but page works normally');
}
```

### 智能批量处理

API现在支持数组参数，我们充分利用这个特性：

```typescript
// 自动批量发送，最多50个请求一批
const batchParams = items.map(item => ({
  userAgent: item.params.userAgent,
  referrer: item.params.referrer,
  pageUrl: item.params.pageUrl,
  contentType: item.params.contentType,
  contentId: item.params.contentId,
}));

await postVisitLog(batchParams); // 一次发送多个统计
```

**批量处理优势**:
- 🚀 **减少网络请求**: 100个统计请求可能只需要2-3次网络调用
- ⚡ **提高效率**: 减少HTTP连接开销和服务器处理负担
- 🛡️ **更好容错**: 批量失败时自动拆分为单个重试
- 📊 **智能分批**: 根据空闲时间动态调整批量大小

### 智能队列管理

1. **防内存泄漏**: 队列大小限制，自动清理旧数据
2. **批量处理**: 在空闲时间批量处理多个请求，最多50个一批
3. **动态分批**: 根据 `deadline.timeRemaining()` 动态调整批量大小
4. **状态监控**: 实时队列状态，便于调试

### 重试机制

智能重试策略，确保数据可靠性：

```typescript
const RETRY_DELAYS = [1000, 3000, 10000]; // 1s, 3s, 10s
// 指数退避，避免网络拥塞
```

## API 参考

### useLogVisit(autoLog?, defaultParams?)

主要的Hook函数

**参数:**

- `autoLog`: boolean - 是否自动记录（默认false）
- `defaultParams`: Partial<VisitLogParams> - 默认参数

**返回值:**

- `logVisit`: (params: VisitLogParams) => void - 手动记录函数（非阻塞）
- `isLogging`: boolean - 是否正在记录中

### 便捷Hooks函数

- `useAutoLogVisit(params)` - 自动记录访问
- `useLogDocumentVisit(documentId)` - 文档访问记录
- `useLogNoticeVisit(noticeId)` - 公告访问记录
- `useLogHomePageVisit()` - 首页访问记录

### 全局函数

- `logVisitGlobal(params)` - 全局记录函数，可在任何地方使用

### 调试和监控工具

- `getVisitLogQueueStatus()` - 获取队列状态，返回队列长度、处理状态等
- `clearVisitLogQueue()` - 清空队列，用于测试或重置

#### 队列状态监控示例

```typescript
import { getVisitLogQueueStatus } from '@/hooks/useLogVisit';

// 实时监控队列状态
setInterval(() => {
  const status = getVisitLogQueueStatus();
  console.log('队列状态:', status);
  // { queueLength: 5, isProcessing: true, hasScheduledCallback: true }
}, 1000);
```

#### 容错测试示例

```typescript
// 即使在错误情况下，页面功能也不受影响
try {
  // 模拟网络错误
  logVisitGlobal({ pageUrl: '/test', contentType: ContentType.Document });

  // 页面功能继续正常工作
  console.log('页面功能正常，统计在后台处理');
} catch (error) {
  // 这里永远不会执行，因为统计是完全非阻塞的
}
```
