import { MenuType } from '@/typings/MenuType';
import { request } from '@umijs/max';

type ResponseData<T> = {
  IsSuccess: boolean;
  Msg: string;
  Data: T;
};

export async function getMenuList(): Promise<ResponseData<MenuType[]>> {
  return request('/api/Menu/GetMenuItemList', {
    headers: {
      'Accept-Language': localStorage.getItem('umi_locale') || 'zh-cn',
    },
  });
}

export async function queryMenuList(
  key: string,
): Promise<ResponseData<MenuType[]>> {
  return request(`/api/Menu/SearchMenuItemList?searchKey=${key}`, {
    headers: {
      'Accept-Language': localStorage.getItem('umi_locale') || 'zh-cn',
    },
  });
}

export async function getDocumentBySlug(
  slug: string,
): Promise<ResponseData<any>> {
  return request(`/api/Document/GetDocumentWithSlug?documentSlug=${slug}`, {
    headers: {
      'Accept-Language': localStorage.getItem('umi_locale') || 'zh-cn',
    },
  });
}

export async function getReferenceMenuItems(
  id: string,
): Promise<ResponseData<MenuType[]>> {
  return request(`/api/Menu/GetReferenceMenuItems?menuItemId=${id}`);
}

export interface Notice {
  Id: number;
  Title: string;
  Content: string;
  TextFormatType: number;
  Priority: number;
  IsTop: boolean;
  UpdatedTime: string; // or Date if you'll parse it
}
export async function getNotices(): Promise<{
  IsSuccess: boolean;
  Msg: string;
  Data: Notice[];
}> {
  return request('/api/Notice/GetPublishedNotices', {
    method: 'GET',
  });
}

export async function getNoticeById(
  id: string,
): Promise<{ IsSuccess: boolean; Msg: string; Data: Notice }> {
  return request(`/api/Notice/GetNotice?noticeId=${id}`, {
    method: 'GET',
  });
}

export async function postVisitLog(params: { userAgent: string; referrer: string; pageUrl: string; contentType: number; contentId: number }[]) {
  return request('/api/VisitLog/LogVisit', {
    method: "POST",
    data: params
  })
}

export async function getStatisticSummary(){
  return request('/api/Statistic/GetStatisticSummary', {
    method: "GET",
  })
}

export async function getStatisticByDate(params:{startDate: string; endDate: string;}){
  return request('/api/Statistic/GetStatisticByDateRange', {
    method: "GET",
    params: params
  })
}

export async function getTopStatistic(){
  return request('/api/Statistic/GetStatisticByDocument?takeTopCount=10')
}