import React, { useState, useCallback, useEffect } from 'react';
import { 
  useLogVisit, 
  logVisitGlobal, 
  getVisitLogQueueStatus,
  clearVisitLogQueue,
  ContentType 
} from './useLogVisit';

// 展示批量处理优势的示例
export const BatchProcessingExample: React.FC = () => {
  const [queueStatus, setQueueStatus] = useState({ queueLength: 0, isProcessing: false, hasScheduledCallback: false });
  const [networkRequests, setNetworkRequests] = useState<Array<{ timestamp: number; count: number; type: string }>>([]);
  const [totalRequests, setTotalRequests] = useState(0);
  const { logVisit } = useLogVisit();

  // 监控网络请求（模拟）
  useEffect(() => {
    const originalFetch = window.fetch;
    let requestCount = 0;

    window.fetch = async (...args) => {
      const url = args[0] as string;
      if (url.includes('/api/VisitLog/LogVisit')) {
        requestCount++;
        setNetworkRequests(prev => [...prev.slice(-9), {
          timestamp: Date.now(),
          count: requestCount,
          type: 'batch'
        }]);
      }
      return originalFetch(...args);
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  // 定期更新队列状态
  useEffect(() => {
    const interval = setInterval(() => {
      try {
        const status = getVisitLogQueueStatus();
        setQueueStatus(status);
      } catch (error) {
        console.warn('Failed to get queue status:', error);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  // 模拟大量快速操作
  const handleMassiveTest = useCallback(() => {
    const startTime = performance.now();
    
    try {
      // 快速生成大量统计请求
      for (let i = 0; i < 200; i++) {
        logVisit({
          pageUrl: `/massive-test-${i}`,
          contentType: ContentType.Document,
          contentId: i,
        });
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setTotalRequests(prev => prev + 200);
      
      console.log(`✅ 200个统计请求入队耗时: ${duration.toFixed(2)}ms`);
    } catch (error) {
      console.warn('Massive test failed:', error);
    }
  }, [logVisit]);

  // 模拟高频滚动
  const handleScrollSimulation = useCallback(() => {
    const startTime = performance.now();
    
    try {
      // 模拟滚动事件产生的大量统计
      for (let i = 0; i < 100; i++) {
        setTimeout(() => {
          logVisit({
            pageUrl: `/scroll-${i}`,
            contentType: ContentType.HomePage,
            contentId: Math.floor(i / 10), // 每10个滚动一个内容区域
          });
        }, i * 10); // 每10ms一个请求，模拟快速滚动
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      setTotalRequests(prev => prev + 100);
      
      console.log(`✅ 100个滚动统计调度耗时: ${duration.toFixed(2)}ms`);
    } catch (error) {
      console.warn('Scroll simulation failed:', error);
    }
  }, [logVisit]);

  // 清空所有数据
  const handleReset = useCallback(() => {
    try {
      clearVisitLogQueue();
      setNetworkRequests([]);
      setTotalRequests(0);
      console.log('✅ 所有数据已重置');
    } catch (error) {
      console.warn('Failed to reset:', error);
    }
  }, []);

  const recentRequests = networkRequests.slice(-5);
  const totalNetworkRequests = networkRequests.length;
  const efficiency = totalRequests > 0 ? ((totalRequests / Math.max(totalNetworkRequests, 1)) * 100).toFixed(1) : '0';

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>🚀 批量处理效果演示</h3>
      
      {/* 统计面板 */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '15px',
        marginBottom: '20px' 
      }}>
        <div style={{ 
          padding: '15px', 
          backgroundColor: '#f0f8ff',
          border: '1px solid #87ceeb',
          borderRadius: '4px'
        }}>
          <h4>📊 队列状态</h4>
          <p>队列长度: <strong>{queueStatus.queueLength}</strong></p>
          <p>正在处理: <strong>{queueStatus.isProcessing ? '是' : '否'}</strong></p>
          <p>已调度: <strong>{queueStatus.hasScheduledCallback ? '是' : '否'}</strong></p>
        </div>

        <div style={{ 
          padding: '15px', 
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '4px'
        }}>
          <h4>🌐 网络效率</h4>
          <p>统计请求数: <strong>{totalRequests}</strong></p>
          <p>网络请求数: <strong>{totalNetworkRequests}</strong></p>
          <p>批量效率: <strong>{efficiency}%</strong></p>
        </div>

        <div style={{ 
          padding: '15px', 
          backgroundColor: '#fff7e6',
          border: '1px solid #ffd591',
          borderRadius: '4px'
        }}>
          <h4>📈 最近网络请求</h4>
          {recentRequests.length > 0 ? (
            recentRequests.map((req, index) => (
              <p key={index} style={{ margin: '2px 0', fontSize: '12px' }}>
                请求 #{req.count} - {new Date(req.timestamp).toLocaleTimeString()}
              </p>
            ))
          ) : (
            <p style={{ color: '#999' }}>暂无网络请求</p>
          )}
        </div>
      </div>

      {/* 测试按钮 */}
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleMassiveTest}
          style={{ 
            padding: '12px 20px', 
            marginRight: '10px',
            marginBottom: '10px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          大量请求测试 (200个)
        </button>
        
        <button 
          onClick={handleScrollSimulation}
          style={{ 
            padding: '12px 20px',
            marginRight: '10px',
            marginBottom: '10px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          滚动模拟测试 (100个)
        </button>
        
        <button 
          onClick={handleReset}
          style={{ 
            padding: '12px 20px',
            marginBottom: '10px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          重置数据
        </button>
      </div>

      {/* 批量处理说明 */}
      <div style={{ 
        padding: '15px', 
        backgroundColor: '#f0f2f5',
        border: '1px solid #d9d9d9',
        borderRadius: '4px'
      }}>
        <h4>🎯 批量处理优势</h4>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '10px' }}>
          <div>
            <h5>📉 减少网络开销</h5>
            <p>200个统计请求可能只需要4-5次网络调用</p>
          </div>
          <div>
            <h5>⚡ 提高响应速度</h5>
            <p>减少HTTP连接建立和关闭的开销</p>
          </div>
          <div>
            <h5>🛡️ 更好的容错性</h5>
            <p>批量失败时自动拆分为单个重试</p>
          </div>
          <div>
            <h5>🎛️ 智能调度</h5>
            <p>根据浏览器空闲时间动态调整批量大小</p>
          </div>
        </div>
      </div>

      {/* 实时效果展示 */}
      <div style={{ 
        marginTop: '15px',
        padding: '15px', 
        backgroundColor: '#e6f7ff',
        border: '1px solid #91d5ff',
        borderRadius: '4px'
      }}>
        <h4>📊 实时效果</h4>
        <p>
          <strong>批量效率 {efficiency}%</strong> 意味着每个网络请求平均处理了 
          <strong> {totalRequests > 0 ? (totalRequests / Math.max(totalNetworkRequests, 1)).toFixed(1) : '0'} </strong>
          个统计请求
        </p>
        <p style={{ color: '#666', fontSize: '14px' }}>
          💡 效率越高说明批量处理效果越好。理想情况下，50个统计请求只需要1次网络调用。
        </p>
      </div>
    </div>
  );
};

export default BatchProcessingExample;
