// import { getImage } from '@/services/api';
import React, { useEffect, useState } from 'react';

interface AsyncImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  token: string;
}

const AsyncImage: React.FC<AsyncImageProps> = ({
  src,
  alt,
  placeholder,
  token,
}) => {
  const [imageSrc, setImageSrc] = useState<string | undefined>(placeholder);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    // const fetchImage = async () => {
    //   try {
    //     const response = await getImage(src);
    //     if (!response) {
    //       throw new Error('Network response was not ok');
    //     }
    //     const imageUrl = URL.createObjectURL(response);
    //     setImageSrc(imageUrl);
    //     setLoading(false);
    //   } catch (error) {
    //     setError(true);
    //     setLoading(false);
    //   }
    // };
    // fetchImage();
  }, [src, token]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error loading image</div>;
  }

  return <img width={'100%'} src={imageSrc} alt={alt} />;
};

export default AsyncImage;
