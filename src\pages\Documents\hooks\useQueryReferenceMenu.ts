import { getReferenceMenuItems } from '@/services/api';
import { useRequest, useSearchParams } from '@umijs/max';

const useQueryReferenceMenu = () => {
  const [searchParams] = useSearchParams();
  const { data, loading } = useRequest(
    () => getReferenceMenuItems(searchParams.get('m') as string),
    {
      refreshDeps: [searchParams.get('m')],
      ready: !!searchParams.get('m'), // 只有当 m 参数存在时才发起请求
    },
  );

  return {
    data,
    loading,
  };
};

export default useQueryReferenceMenu;
