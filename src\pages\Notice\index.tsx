import Notice from '@/components/Notice';
import useNoticesStore, { getNoticesList } from '@/store/notices';
import { getLocale } from '@umijs/max';
import { useSafeState } from 'ahooks';
import { Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding: 0 24px;
  `,
}));

const Announcement = () => {
  const { styles } = useStyles();
  const notices = useNoticesStore();

  const [loading, setLoading] = useSafeState(false);

  useEffect(() => {
    setLoading(true);
    getNoticesList().finally(() => {
      setLoading(false);
    });
  }, [getLocale()]);

  return (
    <div className={styles.container}>
      <Skeleton loading={loading}>
        <Notice items={notices?.list} />
      </Skeleton>
    </div>
  );
};

export default Announcement;
