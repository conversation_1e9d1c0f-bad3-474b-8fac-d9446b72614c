import { UserInfo } from '@/store/user';
import { request } from '@umijs/max';

export async function login(params: {
  username: string;
  password: string;
  uuid: string;
  code: string;
}): Promise<{ Data: UserInfo; IsSuccess: boolean; Msg: string }> {
  return request('/api/Authentication/RequestToken', {
    method: 'POST',
    data: params,
  });
}

export async function getCaptcha(): Promise<{
  IsSuccess: boolean;
  Msg: string;
  Data: { uuid: string; img: string };
}> {
  return request('/api/Captcha/GetCaptcha?codeType=1');
}
