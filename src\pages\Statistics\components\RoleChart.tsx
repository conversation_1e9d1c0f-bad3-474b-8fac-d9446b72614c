import { Bar, BarChart } from '@visactor/react-vchart';
import { FC, useRef } from 'react';

interface BarDataItem {
  [key: string]: string | number; // 允许多个字段，字段名对应 xField、yField、seriesField 等配置
}

interface IRoleChart {
  data: BarDataItem[];
}

const RoleChart: FC<IRoleChart> = ({ data }) => {
  const chartRef = useRef(null);
  return (
    <BarChart ref={chartRef} data={[{ id: 'id0', values: data }]} height={295} >
      <Bar
        xField={'RoleName'}
        yField={'Count'}
      />
    </BarChart>
  );
};

export default RoleChart;
