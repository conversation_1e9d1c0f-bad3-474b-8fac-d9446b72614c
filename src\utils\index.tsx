import { MenuType } from '@/typings/MenuType';
import { MenuProps } from 'antd';
import { MenuItemType, SubMenuType } from 'antd/es/menu/interface';
import { find } from 'lodash-es';
import { Key } from 'react';

export function findDeepObject(array: any[], key: string, value: any): any {
  return find(array, (item) => {
    if (item[key] === value) return true;
    if (item.children) return findDeepObject(item.children, key, value);
    return false;
  });
}

export interface LevelKeysProps {
  key?: string;
  children?: LevelKeysProps[];
}

export const getLevelKeys = (items1: LevelKeysProps[]) => {
  const key: Record<string, number> = {};
  const func = (items2: LevelKeysProps[], level = 1) => {
    items2.forEach((item) => {
      if (item.key) {
        key[item.key] = level;
      }
      if (item.children) {
        func(item.children, level + 1);
      }
    });
  };
  func(items1);
  return key;
};
type MenuItem = Required<MenuProps>['items'][number];
// 查找指定 key 的菜单项的所有父级 SubMenu 的 key 数组
export function findParentSubmenuKeys(
  menuItems: MenuItem[],
  targetKey: Key,
): Key[] {
  const result: Key[] = [];

  function traverse(items: MenuItem[], parentKeys: Key[]) {
    if (!items.length) return;
    for (const item of items) {
      if (item?.key === targetKey) {
        result.push(...parentKeys);
      }
      if ((item as SubMenuType)?.children) {
        traverse((item as SubMenuType).children, [
          ...parentKeys,
          (item as SubMenuType).key,
        ]);
      }
    }
  }

  traverse(menuItems, []);
  return result;
}

export function convertMenuToAntdItems(menuData: MenuType[]) {
  const parentMap = new Map<number, MenuType[]>();
  menuData.forEach((menu) => {
    const parentId = menu.ParentId;
    if (!parentMap.has(parentId)) {
      parentMap.set(parentId, []);
    }
    parentMap.get(parentId)?.push(menu);
  });

  const buildMenuTree = (parentId: number): MenuItem[] => {
    return (
      parentMap
        .get(parentId)
        ?.sort((a, b) => a.Sort - b.Sort)
        .map((menu) => ({
          key: menu.DocumentSlug
            ? menu.DocumentSlug
            : menu.Type === 2
            ? 'notice-page'
            : menu.Id.toString(),
          label: (
            <span
              style={{
                display: 'block',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
              }}
              title={menu.Name}
              data-documentid={menu.DocumentSlug}
            >
              {menu.Name}
            </span>
          ),
          title: menu.Name,
          ...(parentMap.has(menu.Id)
            ? {
                children: buildMenuTree(menu.Id) as MenuItem[],
              }
            : {}),
        })) || []
    );
  };

  return buildMenuTree(0);
}

export function findPathName(
  data: MenuItem[],
  targetKey: string,
  currentPath: string[] = [],
): string[] {
  if (!data.length || !targetKey) return [];
  for (const item of data) {
    const newPath: string[] = [
      ...currentPath,
      (item as MenuItemType).title as string,
    ];
    if (item?.key === targetKey) {
      return newPath;
    }
    if ((item as SubMenuType)?.children) {
      const foundPath = findPathName(
        (item as SubMenuType).children,
        targetKey,
        newPath,
      );
      if (foundPath.length) {
        return foundPath;
      }
    }
  }
  return [];
}

export function formatStringToSlug(input: string) {
  // 1. 转为小写
  // 2. 用"-"替换所有空格
  return input.toLowerCase().replace(/\s+/g, '-');
}
