import { getDocumentStatistic, getStatisticByRole, getStatisticSummary } from "@/services/api"
import { useRequest } from "ahooks"

const useStatisticSummary = () => {
  const { data: statisticSummary, loading: loadingSummary } = useRequest(getStatisticSummary)

  const { run: runGetStatisticByRole, data: statisticByRole, loading: loadingByRole } = useRequest(getStatisticByRole, {
    manual: true,
  })

  const { run: runGetDocumentStatistic, data: documentStatistic, loading: loadingDocumentStatistic } = useRequest(getDocumentStatistic, {
    manual: true,
  })

  return {
    statisticSummary,
    statisticByRole,
    documentStatistic,
    runGetStatisticByRole,
    runGetDocumentStatistic,
    loadingSummary,
    loadingByRole,
    loadingDocumentStatistic,
  }
}

export default useStatisticSummary
