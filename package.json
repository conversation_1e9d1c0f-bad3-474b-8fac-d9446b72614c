{"private": true, "author": "Sun<PERSON><PERSON>ao <<EMAIL>>", "scripts": {"build": "max build", "build:pro": "cross-env UMI_ENV=pro max build", "build:uat": "cross-env UMI_ENV=uat max build", "dev": "max dev", "dev:uat": "cross-env UMI_ENV=uat max dev", "format": "prettier --cache --write .", "postinstall": "max setup", "prepare": "husky", "setup": "max setup", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.4.4", "@umijs/max": "^4.4.6", "ahooks": "^3.8.4", "antd": "^5.4.0", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "derive-valtio": "^0.2.0", "lodash-es": "^4.17.21", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "valtio": "^2.1.5", "valtio-persist": "^2.2.3"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "antd-style": "^3.7.1", "husky": "^9", "lint-staged": "^13.2.0", "prettier": "^2.8.7", "prettier-plugin-organize-imports": "^3.2.2", "prettier-plugin-packagejson": "^2.4.3", "typescript": "^5.0.3"}}