import { deriveuserStore } from '@/store/user';
import { FormattedMessage, useIntl, useParams } from '@umijs/max';
import { Breadcrumb, Flex } from 'antd';
import { createStyles, cx } from 'antd-style';
import ArticleHeader from '../Documents/components/ArticleHeader';
import LoginHome from './LoginHome';
import { useLogHomePageVisit } from '@/hooks';

const useStyles = createStyles(({ css }) => ({
  wrapper: css`
    padding: 10px 40px 80px;
  `,
  container: css`
    width: 100%;
    font-family: 'Microsoft YaHei', sans-serif;
  `,
  img: css`
    width: 85%;
    align-self: center;
  `,
  title: css`
    font-size: 26px;
    font-weight: bold;
    color: #0078d4;
    &.black {
      color: #333;
      font-weight: 500;
    }
  `,
  one: css`
    width: 100%;
    background: linear-gradient(to right, #eee, #fff);
    padding: 20px 40px;
    border-left: 6px solid #0078d4;
    .description {
      font-size: 16px;
      line-height: 1.5;
      color: #333;
    }
  `,
  two: css`
    width: 100%;
    background: linear-gradient(to right, #eee, #fff);
    padding: 20px 40px;
    border-left: 6px solid #0078d4;
    .description {
      font-size: 16px;
      line-height: 1.5;
      color: #7f7f7f;
    }
  `,
  three: css`
    width: 100%;
    background: linear-gradient(to right, #eee, #fff);
    padding: 20px 40px;
    border-left: 6px solid #0078d4;
  `,
}));

const Home = () => {
  const { styles } = useStyles();
  const params = useParams();
  const intl = useIntl();
  const title = intl.formatMessage({
    id: 'home.title',
  });

  useLogHomePageVisit()

  if (deriveuserStore.isLogin) {
    return <LoginHome />;
  }
  return (
    <div className={styles.wrapper}>
      <Breadcrumb items={[{ title: '21VCA-E Playbook' }]} />
      <ArticleHeader author={''} title={title} date={'2025-04-18'} />
      <Flex
        vertical
        justify="center"
        align="center"
        gap={60}
        className={styles.container}
      >
        <Flex vertical gap={40}>
          <Flex vertical gap={10} className={styles.one}>
            <div className={styles.title}>
              <FormattedMessage id={'home.one.title'} />{' '}
            </div>
            <div className="description">
              <FormattedMessage id={'home.one.sub'} />
            </div>
          </Flex>
          <Flex vertical gap={40} justify="center" align="center">
            <div className={cx(styles.title, 'black')}>
              <FormattedMessage id={'home.one.desc'} />
            </div>
            <img
              src={require(`@/assets/home_${params.lang}1.png`)}
              className={styles.img}
            />
          </Flex>
        </Flex>
        <Flex vertical justify="center" align="center" gap={40}>
          <Flex vertical gap={1} className={styles.two}>
            <div className={styles.title}>
              <FormattedMessage id={'home.two.title'} />
            </div>
            <div className="description">
              <FormattedMessage id={'home.two.sub'} />
            </div>
          </Flex>
          <img
            src={require(`@/assets/home_${params.lang}2.png`)}
            className={styles.img}
          />
        </Flex>
        <Flex vertical justify="center" align="center" gap={40}>
          <Flex vertical gap={10} className={styles.three}>
            <div className={styles.title}>
              <FormattedMessage id={'home.three.title'} />
            </div>
          </Flex>
          <img
            src={require(`@/assets/home_${params.lang}3.png`)}
            className={styles.img}
          />
        </Flex>
      </Flex>
    </div>
  );
};
export default Home;
