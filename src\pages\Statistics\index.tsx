import { Card, Col, DatePicker, Row, Statistic, Table } from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat);

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding-block: 24px;
    padding-inline: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  `,
}));

const Statistics = () => {
  const { styles } = useStyles();

  return (
    <div className={styles.container}>
      <Row gutter={24}>
        <Col span={8}>
          <Card variant="borderless">
            <Statistic title="Total Sales" value={112893} />
          </Card>
        </Col>
        <Col span={8}>
          <Card variant="borderless">
            <Statistic title="Total Sales" value={112893} />
          </Card>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Card
            title="产品访问量"
            extra={
              <>
                <DatePicker.RangePicker
                  defaultValue={[dayjs(), dayjs()]}
                  maxDate={dayjs()}
                  format={'YYYY-MM-DD'}
                />
              </>
            }
          >
            <Table
              columns={[
                { title: '名字', dataIndex: 'title' },
                { title: '访问量', dataIndex: 'count' },
              ]}
              dataSource={[
                {
                  title: 'ssss',
                  count: 123,
                },
              ]}
              size="small"
              pagination={{
                current: 1,
                pageSize: 10,
                total: 0,
                size: 'small',
                style: {
                  marginBottom: 0,
                },
              }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="当月产品访问量"></Card>
        </Col>
      </Row>
    </div>
  );
};

export default Statistics;
