import { useMount } from 'ahooks';
import { Card, Col, DatePicker, Row, Skeleton, Statistic, Table } from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { useRef } from 'react';
import RoleChart from './components/RoleChart';
import useStatisticSummary from './hooks/useStatisticSummary';

dayjs.extend(customParseFormat);

const useStyles = createStyles(({ css }) => ({
  container: css`
    padding-block: 24px;
    padding-inline: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  `,
}));

const Statistics = () => {
  const { styles } = useStyles();
  const chartRef = useRef(null);
  const {
    statisticSummary,
    statisticByRole,
    documentStatistic,
    runGetStatisticByRole,
    runGetDocumentStatistic,
    loadingSummary,
    loadingByRole,
    loadingDocumentStatistic,
  } = useStatisticSummary();

  useMount(() => {
    runGetDocumentStatistic({
      startDate: dayjs('2025-07-01').format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
    });
    runGetStatisticByRole({
      startDate: dayjs('2025-07-01').format('YYYY-MM-DD'),
      endDate: dayjs().format('YYYY-MM-DD'),
    });
  });

  return (
    <div className={styles.container}>
      <Row gutter={24}>
        <Col span={8}>
          <Skeleton loading={loadingSummary}>
            <Card variant="borderless">
              <Statistic
                title="总访问量"
                value={statisticSummary?.Data.TotalCount}
              />
            </Card>
          </Skeleton>
        </Col>
        <Col span={8}>
          <Card variant="borderless">
            <Statistic
              title="当月访问量"
              value={statisticSummary?.Data.CurrentMonthCount}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card variant="borderless">
            <Statistic
              title="今天访问量"
              value={statisticSummary?.Data.CurrentDayCount}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={12}>
          <Card
            title="产品访问量"
            extra={
              <>
                <DatePicker.RangePicker
                  defaultValue={[dayjs(), dayjs()]}
                  maxDate={dayjs()}
                  format={'YYYY-MM-DD'}
                />
              </>
            }
          >
            <Table
              columns={[
                {
                  title: '名字',
                  dataIndex: 'DocumentTitle',
                  key: 'DocumentTitle',
                },
                { title: '访问量', dataIndex: 'TotalCount', key: 'TotalCount' },
              ]}
              rowKey={'DocumentId'}
              dataSource={documentStatistic?.Data}
              size="small"
              pagination={{
                current: 1,
                pageSize: 5,
                total: documentStatistic?.Data.length,
                size: 'small',
                style: {
                  marginBottom: 0,
                },
              }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="当月产品访问量">
            <RoleChart data={statisticByRole?.Data || []}></RoleChart>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Statistics;
