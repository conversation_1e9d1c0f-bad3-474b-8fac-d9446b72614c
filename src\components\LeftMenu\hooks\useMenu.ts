import { getMenuList, queryMenuList } from '@/services/api';
import { deriveuserStore } from '@/store/user';
import {
  convertMenuToAntdItems,
  findParentSubmenuKeys,
  findPathName,
  getLevelKeys,
  LevelKeysProps,
} from '@/utils';
import { getLocale, useModel, useParams, useRequest } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import { MenuProps } from 'antd';
import { useEffect, useState } from 'react';

type MenuItem = Required<MenuProps>['items'][number];

const useMenu = () => {
  const { setBreadcrumb, setMenu } = useModel('global');
  const [stateOpenKeys, setStateOpenKeys] = useState<string[]>([]); // 打开的菜单
  const [open, setOpen] = useState<boolean>(false); // 是否打开查询菜单区域
  const [searchValue, setSearchValue] = useState<string>(''); // 查询菜单的值

  const [items, setItems] = useState<MenuItem[]>([]); // 菜单项

  const params = useParams();

  const { data, loading, error } = useRequest(getMenuList, {
    refreshDeps: [getLocale(), deriveuserStore.isLogin],
    onSuccess: (res) => {
      const menuItems = convertMenuToAntdItems(res);
      setItems(menuItems);
      setMenu(res);
    },
  });

  const levelKeys = getLevelKeys(items as LevelKeysProps[]);
  const onOpenChange: MenuProps['onOpenChange'] = (openKeys) => {
    const currentOpenKey = openKeys.find(
      (key) => stateOpenKeys.indexOf(key) === -1,
    );
    // open
    if (currentOpenKey !== undefined) {
      const repeatIndex = openKeys
        .filter((key) => key !== currentOpenKey)
        .findIndex((key) => levelKeys[key] === levelKeys[currentOpenKey]);

      setStateOpenKeys(
        openKeys
          // remove repeat key
          .filter((_, index) => index !== repeatIndex)
          // remove current level all child
          .filter((key) => levelKeys[key] <= levelKeys[currentOpenKey]),
      );
    } else {
      // close
      setStateOpenKeys(openKeys);
    }
  };

  const {
    run,
    loading: queryLoading,
    data: queryData,
  } = useRequest(queryMenuList, {
    manual: true,
    ready: !!searchValue,
    refreshDeps: [searchValue],
    debounceInterval: 500,
  });

  const handleOpenMenu = useMemoizedFn((currentKey: string) => {
    if (currentKey) {
      const openKeys = findParentSubmenuKeys(items, currentKey);
      setStateOpenKeys(openKeys as string[]);
    }
  });

  useEffect(() => {
    setBreadcrumb(findPathName(items, params.slug ?? ''));
  }, [items, params.slug]);

  return {
    queryMenu: run, // 查询菜单
    queryLoading,
    queryData,
    data,
    loading,
    error,
    open,
    setOpen,
    stateOpenKeys,
    setStateOpenKeys,
    items,
    handleOpenMenu,
    searchValue,
    setSearchValue,
    levelKeys,
    onOpenChange,
  };
};

export default useMenu;
