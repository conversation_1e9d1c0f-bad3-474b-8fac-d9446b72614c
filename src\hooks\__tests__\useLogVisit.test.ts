import { renderHook, act } from '@testing-library/react';
import { useLogVisit, ContentType } from '../useLogVisit';
import * as api from '@/services/api';

// Mock the API
jest.mock('@/services/api', () => ({
  postVisitLog: jest.fn(),
}));

const mockPostVisitLog = api.postVisitLog as jest.MockedFunction<typeof api.postVisitLog>;

// Mock browser APIs
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-path',
    search: '?param=value',
  },
  writable: true,
});

Object.defineProperty(document, 'referrer', {
  value: 'https://example.com/referrer',
  writable: true,
});

Object.defineProperty(navigator, 'userAgent', {
  value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  writable: true,
});

describe('useLogVisit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockPostVisitLog.mockResolvedValue({} as any);
  });

  it('should initialize with correct default values', () => {
    const { result } = renderHook(() => useLogVisit());
    
    expect(result.current.isLogging).toBe(false);
    expect(typeof result.current.logVisit).toBe('function');
  });

  it('should call postVisitLog with correct parameters', async () => {
    const { result } = renderHook(() => useLogVisit());
    
    const params = {
      pageUrl: '/test-page',
      contentType: ContentType.Document,
      contentId: 123,
    };

    await act(async () => {
      await result.current.logVisit(params);
    });

    expect(mockPostVisitLog).toHaveBeenCalledWith({
      pageUrl: '/test-page',
      contentType: ContentType.Document,
      contentId: 123,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      referrer: 'https://example.com/referrer',
    });
  });

  it('should use custom userAgent and referrer when provided', async () => {
    const { result } = renderHook(() => useLogVisit());
    
    const params = {
      pageUrl: '/test-page',
      contentType: ContentType.HomePage,
      userAgent: 'Custom User Agent',
      referrer: 'https://custom-referrer.com',
    };

    await act(async () => {
      await result.current.logVisit(params);
    });

    expect(mockPostVisitLog).toHaveBeenCalledWith({
      pageUrl: '/test-page',
      contentType: ContentType.HomePage,
      contentId: 0,
      userAgent: 'Custom User Agent',
      referrer: 'https://custom-referrer.com',
    });
  });

  it('should handle API errors gracefully', async () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    mockPostVisitLog.mockRejectedValue(new Error('API Error'));
    
    const { result } = renderHook(() => useLogVisit());
    
    const params = {
      pageUrl: '/test-page',
      contentType: ContentType.Notice,
      contentId: 456,
    };

    await act(async () => {
      await result.current.logVisit(params);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Failed to record visit log:', expect.any(Error));
    consoleSpy.mockRestore();
  });

  it('should prevent concurrent logging', async () => {
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
    mockPostVisitLog.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    const { result } = renderHook(() => useLogVisit());
    
    const params = {
      pageUrl: '/test-page',
      contentType: ContentType.Document,
      contentId: 123,
    };

    // Start first log
    act(() => {
      result.current.logVisit(params);
    });

    // Try to start second log immediately
    await act(async () => {
      await result.current.logVisit(params);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Visit log is already being recorded');
    expect(mockPostVisitLog).toHaveBeenCalledTimes(1);
    
    consoleSpy.mockRestore();
  });

  it('should auto-log when autoLog is true', async () => {
    const defaultParams = {
      contentType: ContentType.HomePage,
      contentId: 789,
    };

    renderHook(() => useLogVisit(true, defaultParams));

    // Wait for useEffect to run
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockPostVisitLog).toHaveBeenCalledWith({
      pageUrl: '/test-path?param=value',
      contentType: ContentType.HomePage,
      contentId: 789,
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      referrer: 'https://example.com/referrer',
    });
  });

  it('should not auto-log when autoLog is false', async () => {
    const defaultParams = {
      contentType: ContentType.HomePage,
    };

    renderHook(() => useLogVisit(false, defaultParams));

    // Wait for potential useEffect to run
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockPostVisitLog).not.toHaveBeenCalled();
  });

  it('should not auto-log multiple times for the same component instance', async () => {
    const defaultParams = {
      contentType: ContentType.HomePage,
    };

    const { rerender } = renderHook(() => useLogVisit(true, defaultParams));

    // Wait for first auto-log
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Rerender the hook
    rerender();

    // Wait for potential second auto-log
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(mockPostVisitLog).toHaveBeenCalledTimes(1);
  });
});
