import React, { useState, useCallback } from 'react';
import { useLogVisit, logVisitGlobal, ContentType } from './useLogVisit';

// 性能测试示例：展示非阻塞特性
export const PerformanceTestExample: React.FC = () => {
  const [clickCount, setClickCount] = useState(0);
  const [renderTime, setRenderTime] = useState<number[]>([]);
  const { logVisit } = useLogVisit();

  // 测试大量快速点击的性能
  const handleRapidClicks = useCallback(() => {
    const startTime = performance.now();
    
    // 模拟快速连续点击
    for (let i = 0; i < 10; i++) {
      logVisit({
        pageUrl: `/rapid-click-${i}`,
        contentType: ContentType.Document,
        contentId: i,
      });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    setClickCount(prev => prev + 10);
    setRenderTime(prev => [...prev.slice(-9), duration]); // 保留最近10次记录
    
    console.log(`✅ 10次统计请求处理耗时: ${duration.toFixed(2)}ms`);
  }, [logVisit]);

  // 测试全局函数性能
  const handleGlobalRapidClicks = useCallback(() => {
    const startTime = performance.now();
    
    // 使用全局函数
    for (let i = 0; i < 10; i++) {
      logVisitGlobal({
        pageUrl: `/global-rapid-click-${i}`,
        contentType: ContentType.HomePage,
        contentId: i,
      });
    }
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    setClickCount(prev => prev + 10);
    setRenderTime(prev => [...prev.slice(-9), duration]);
    
    console.log(`✅ 10次全局统计请求处理耗时: ${duration.toFixed(2)}ms`);
  }, []);

  // 计算平均响应时间
  const averageTime = renderTime.length > 0 
    ? (renderTime.reduce((a, b) => a + b, 0) / renderTime.length).toFixed(2)
    : '0';

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>🚀 性能测试示例</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>统计信息:</h4>
        <p>总点击次数: <strong>{clickCount}</strong></p>
        <p>平均响应时间: <strong>{averageTime}ms</strong></p>
        <p>最近响应时间: {renderTime.slice(-3).map(t => t.toFixed(2)).join('ms, ')}ms</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={handleRapidClicks}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          快速点击测试 (Hooks版本)
        </button>
        
        <button 
          onClick={handleGlobalRapidClicks}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          快速点击测试 (全局函数版本)
        </button>
      </div>

      <div style={{ 
        padding: '15px', 
        backgroundColor: '#f6ffed', 
        border: '1px solid #b7eb8f',
        borderRadius: '4px'
      }}>
        <h4>✅ 性能优势:</h4>
        <ul>
          <li>每次点击响应时间通常 &lt; 1ms</li>
          <li>UI不会因为网络请求而卡顿</li>
          <li>支持高频率操作而不影响性能</li>
          <li>统计请求在后台智能批处理</li>
          <li>失败请求自动重试，无需用户干预</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '15px',
        padding: '15px', 
        backgroundColor: '#fff7e6', 
        border: '1px solid #ffd591',
        borderRadius: '4px'
      }}>
        <h4>🔍 监控建议:</h4>
        <p>打开浏览器开发者工具的Network面板，观察:</p>
        <ul>
          <li>统计请求是批量发送的</li>
          <li>请求不会阻塞页面交互</li>
          <li>失败的请求会自动重试</li>
        </ul>
      </div>
    </div>
  );
};

// 渲染性能对比示例
export const RenderPerformanceExample: React.FC = () => {
  const [items, setItems] = useState<number[]>([]);
  const { logVisit } = useLogVisit();

  // 添加大量项目并记录访问
  const addManyItems = useCallback(() => {
    const startTime = performance.now();
    
    const newItems = Array.from({ length: 100 }, (_, i) => items.length + i);
    
    // 为每个新项目记录访问
    newItems.forEach(item => {
      logVisit({
        pageUrl: `/item-${item}`,
        contentType: ContentType.Document,
        contentId: item,
      });
    });
    
    setItems(prev => [...prev, ...newItems]);
    
    const endTime = performance.now();
    console.log(`✅ 添加100个项目并记录访问耗时: ${(endTime - startTime).toFixed(2)}ms`);
  }, [items.length, logVisit]);

  const clearItems = useCallback(() => {
    setItems([]);
  }, []);

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '10px' }}>
      <h3>📊 渲染性能测试</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <p>当前项目数量: <strong>{items.length}</strong></p>
        
        <button 
          onClick={addManyItems}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: '#722ed1',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          添加100个项目 + 记录访问
        </button>
        
        <button 
          onClick={clearItems}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#ff4d4f',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          清空
        </button>
      </div>

      <div style={{ 
        maxHeight: '200px', 
        overflowY: 'auto',
        border: '1px solid #d9d9d9',
        padding: '10px',
        backgroundColor: '#fafafa'
      }}>
        {items.map(item => (
          <div key={item} style={{ padding: '2px 0' }}>
            项目 {item}
          </div>
        ))}
      </div>

      <div style={{ 
        marginTop: '15px',
        padding: '15px', 
        backgroundColor: '#f6ffed', 
        border: '1px solid #b7eb8f',
        borderRadius: '4px'
      }}>
        <p><strong>测试说明:</strong> 即使同时添加大量项目并记录访问，页面渲染依然流畅，不会出现卡顿。</p>
      </div>
    </div>
  );
};

// 组合示例
export const PerformanceExamples: React.FC = () => {
  return (
    <div>
      <h2>🚀 useLogVisit 性能测试示例</h2>
      <PerformanceTestExample />
      <RenderPerformanceExample />
    </div>
  );
};

export default PerformanceExamples;
