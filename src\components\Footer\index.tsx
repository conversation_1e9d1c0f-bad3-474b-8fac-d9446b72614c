import { FormattedMessage, Link, useParams } from '@umijs/max';
import { Flex } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  footer: css`
    width: 100%;
    color: #fff;
    font-size: 14px;
    .bg {
      background: url(${require('@/assets/bg.jpg')}) no-repeat center;
      background-size: cover;
      padding: 40px 0;
    }
  `,
  menu: css`
    width: 75%;
    margin: 0 auto;
    a {
      color: #1891f9;
    }
    // & > div {
    //   flex: 1;
    //   h5 {
    //     margin-bottom: 24px;
    //     color: #fff;
    //     font-size: 15px;
    //   }
    //   div {
    //     margin-bottom: 14px;
    //   }
    //   a {
    //     margin-bottom: 14px;
    //     display: block;
    //     color: #dbdbdb;
    //     &:hover {
    //       color: #fff;
    //   }
    //}
  `,
  wechat: css`
    padding: 4px;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
  `,
  beianWrapper: css`
    background: #222;
    font-size: 12px;
  `,
  beian: css`
    width: 75%;
    margin: 0 auto;
    padding: 20px 0;
    color: #ccc;
    a {
      color: #ccc;
    }
  `,
}));

const Footer = () => {
  const { styles } = useStyles();
  const params = useParams();
  return (
    <footer className={styles.footer}>
      <div className="bg">
        <div className={styles.menu}>
          <FormattedMessage id="disclaimer.title.0" />
          <a
            href="https://support.azure.cn/support/support-azure"
            target="_blank"
            rel="noreferrer"
          >
            <FormattedMessage id="disclaimer.title.1" />
          </a>
          <FormattedMessage id="disclaimer.title.2" />
        </div>
      </div>
      <div className={styles.beianWrapper}>
        <Flex className={styles.beian} justify="space-between" align="center">
          <Flex>
            <div>
              © Copyright 2022. 21Vianet Group, Inc. All Rights Reserved.
              {/* <a href="https://beian.miit.gov.cn/#/Integrated/index">
                沪 ICP 备 13015306 号-27
              </a>
              <img src={require('@/assets/beian.png')} width={16} />
              <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011502002131">
                沪公安备31011502002131
              </a> */}
            </div>
          </Flex>
          <Flex gap={20}>
            <Link to={`/${params.lang}/disclaimer`}>
              <FormattedMessage id={'footer.disclaimer'} />
            </Link>
            {/* <a
              href="https://www.21vbluecloud.com/terms-of-use/"
              target="_blank"
              rel="noopener noreferrer"
            >
              使用条款
            </a> */}
            {/* <a href="https://www.21vbluecloud.com/sitemap/" target='_blank' rel='noopener noreferrer'>网站地图</a> */}
          </Flex>
        </Flex>
      </div>
    </footer>
  );
};

export default Footer;
