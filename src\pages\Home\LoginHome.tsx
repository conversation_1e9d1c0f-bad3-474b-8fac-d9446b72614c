import Notice from '@/components/Notice';
import { useIntl, useParams } from '@umijs/max';
import { Col, Flex, Row, Skeleton, Typography } from 'antd';
import { createStyles } from 'antd-style';
import useHome from './hooks';

const useStyles = createStyles(({ css }) => ({
  container: css`
    width: 100%;
    padding: 10px 24px 80px;
  `,
  title: css`
    font-size: 30px;
    font-weight: 500;
    color: rgba(56, 56, 56, 1);
  `,
  card: css`
    padding: 27px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(229, 229, 229, 1);
    transition: all 0.3s ease;
    &:hover {
      background: linear-gradient(
        227.63deg,
        rgba(241, 227, 255, 1) 0%,
        rgba(199, 208, 255, 0.61) 53.33%,
        rgba(176, 230, 255, 0.26) 100%
      );
    }
  `,
  cardTitle: css`
    height: 37px;
    font-size: 16px;
    font-weight: 500;
    color: rgba(56, 56, 56, 1);
  `,
  cardContent: css`
    height: 44px;
    font-size: 14px;
    color: rgba(128, 128, 128, 1);
  `,

  image: css`
    max-width: 100%;
    height: auto;
  `,
}));

const LoginHome = () => {
  const { styles } = useStyles();
  const params = useParams();
  const { data: notices, loading } = useHome();
  const intl = useIntl();

  const renderIntl = (id: string) => {
    return intl.formatMessage({
      id: id,
    });
  };

  return (
    <Flex vertical className={styles.container} gap={24}>
      <Skeleton loading={loading}>
        <Notice showMore items={notices?.Data.slice(0, 3) || []} />
      </Skeleton>
      <Row gutter={24}>
        <Col span={6}>
          <a href={`/${params.lang}/document/introduction-to-new-commerce`}>
            <Flex vertical gap={10} className={styles.card}>
              <img
                className={styles.image}
                src={require('@/assets/loginImage1.png')}
              />
              <div className={styles.cardTitle}>
                {renderIntl('loginHome.0.title')}
              </div>
              <Typography.Paragraph
                ellipsis={{ rows: 2 }}
                className={styles.cardContent}
                title={renderIntl('loginHome.0.desc')}
              >
                {renderIntl('loginHome.0.desc')}
              </Typography.Paragraph>
            </Flex>
          </a>
        </Col>
        <Col span={6}>
          <a href={`/${params.lang}/document/partner-onboarding`}>
            <Flex vertical gap={10} className={styles.card}>
              <img
                className={styles.image}
                src={require('@/assets/loginImage2.png')}
              />
              <div className={styles.cardTitle}>
                {renderIntl('loginHome.1.title')}
              </div>
              <Typography.Paragraph
                ellipsis={{ rows: 2 }}
                className={styles.cardContent}
                title={renderIntl('loginHome.1.desc')}
              >
                {renderIntl('loginHome.1.desc')}
              </Typography.Paragraph>
            </Flex>
          </a>
        </Col>
        <Col span={6}>
          <a href={`/${params.lang}/document/21vianet-customer-agreement`}>
            <Flex vertical gap={10} className={styles.card}>
              <img
                className={styles.image}
                src={require('@/assets/loginImage3.png')}
              />
              <div className={styles.cardTitle}>
                {renderIntl('loginHome.2.title')}
              </div>
              <Typography.Paragraph
                ellipsis={{ rows: 2 }}
                className={styles.cardContent}
                title={renderIntl('loginHome.2.desc')}
              >
                {renderIntl('loginHome.2.desc')}
              </Typography.Paragraph>
            </Flex>
          </a>
        </Col>
        <Col span={6}>
          <a href={`/${params.lang}/document/what-azure-ri`}>
            <Flex vertical gap={10} className={styles.card}>
              <img
                className={styles.image}
                src={require('@/assets/loginImage4.png')}
              />
              <div className={styles.cardTitle}>
                {renderIntl('loginHome.3.title')}
              </div>
              <Typography.Paragraph
                ellipsis={{ rows: 2 }}
                className={styles.cardContent}
                title={renderIntl('loginHome.3.desc')}
              >
                {renderIntl('loginHome.3.desc')}
              </Typography.Paragraph>
            </Flex>
          </a>
        </Col>
      </Row>
    </Flex>
  );
};

export default LoginHome;
