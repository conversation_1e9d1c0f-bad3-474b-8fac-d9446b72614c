import { Image } from 'antd';
import { createStyles } from 'antd-style';
import { FC } from 'react';
import Markdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import RemarkBreaks from 'remark-breaks';
import remarkGfm from 'remark-gfm';

const useStyles = createStyles(({ css }) => ({
  content: css`
    a {
      word-break: break-all;
    }
    ol li,
    ul li {
      line-height: 1.5;
      font-size: 14px;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      font-weight: 600;
    }
    p {
      font-size: 14px;
      line-height: 23.17px;
      margin-top: 0;
      margin-bottom: 16px;
    }
    table {
      position: relative;
      border: none;
      border-collapse: collapse;
      empty-cells: show;
      // overflow: auto;
      border-spacing: 0;
      width: 100%;
      // white-space: nowrap;
      word-break: keep-all;
    }
    table tr {
      background-color: #fafbfc;
      border-top: 1px solid #c6cbd1;
    }
    table thead tr {
      background-color: #0078d4;
      font-weight: 600;
      color: #fff;
    }
    table td,
    table th {
      padding: 6px 13px;
      border: 1px solid #dfe2e5;
      word-break: normal;
      line-height: 1.5;
    }
    table th {
      white-space: nowrap;
    }
    table td:first-child::after,
    table th:first-child::after {
      content: '';
      display: inline-block;
      vertical-align: top;
      min-height: 24px;
    }
    table th {
      font-weight: 600;
    }
    table tbody {
      height: 100%;
    }
    table thead tr th:first-child,
    table tbody tr td:first-child {
      position: sticky;
      left: -2px;
      z-index: 1;
      background: #f2f2f2;
    }
    table thead tr {
      position: sticky;
      left: 0;
      top: -1px;
      z-index: 2;
    }
    table thead tr th:first-child {
      background: #0078d4;
      border-right: 1px solid #fff;
      text-align: left;
    }
    table tbody tr td:first-child {
      text-align: left;
    }
    table tbody tr:nth-child(2n) {
      background: #fff;
    }
    table tbody tr td {
      text-align: center;
    }
    .ant-image {
      margin: 16px 0;
    }
    blockquote {
      border: 1px solid #ccc;
      padding: 10px;
      background-color: #f5f5f5;
      margin-left: 0;
    }
  `,
  tableContainer: css`
    overflow: auto;
    width: 100%;
    max-height: calc(100vh - 230px);
    margin-bottom: 16px;
    table.merge-cell-table {
      table-layout: auto;
      tbody tr:not(.merge-row) td:nth-child(2) {
        text-align: left;
      }
      tr.merge-row {
        td:nth-child(2) {
          text-align: left;
        }
        td:nth-child(3) {
          text-align: left;
          background: #f2f2f2;
        }
        td:nth-child(4) {
          text-align: left;
        }
      }
    }
  `,
}));
const MarkdownContent: FC<{ content: string }> = ({ content = '' }) => {
  const { styles } = useStyles();
  return (
    <div className={styles.content}>
      <Image.PreviewGroup>
        <Markdown
          remarkPlugins={[remarkGfm, RemarkBreaks]}
          rehypePlugins={[rehypeRaw]}
          components={{
            img: ({ node, ...props }) => {
              return <Image src={props.src} alt={props.alt} />;
            },
            a: ({ node, ...props }) => {
              return (
                <a
                  {...props}
                  target="_blank"
                  rel="noreferrer"
                  style={{ color: 'rgba(17, 75, 237, 1)' }}
                />
              );
            },
            table: ({ node, ...props }) => {
              return (
                <div className={styles.tableContainer}>
                  <table {...props} />
                </div>
              );
            },
          }}
          remarkRehypeOptions={{ passThrough: ['link'] }}
        >
          {content}
        </Markdown>
      </Image.PreviewGroup>
    </div>
  );
};

export default MarkdownContent;
