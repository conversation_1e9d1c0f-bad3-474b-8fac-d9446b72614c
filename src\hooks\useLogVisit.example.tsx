import React from 'react';
import { 
  useLogVisit, 
  useAutoLogVisit, 
  useLogDocumentVisit, 
  useLogNoticeVisit, 
  useLogHomePageVisit,
  ContentType 
} from './useLogVisit';

// 示例1: 手动记录访问日志
export const ManualLogExample: React.FC = () => {
  const { logVisit, isLogging } = useLogVisit();

  const handleLogVisit = async () => {
    await logVisit({
      pageUrl: '/documents/example',
      contentType: ContentType.Document,
      contentId: 123,
      // userAgent 和 referrer 会自动获取，也可以手动指定
    });
  };

  return (
    <div>
      <button onClick={handleLogVisit} disabled={isLogging}>
        {isLogging ? '记录中...' : '记录访问'}
      </button>
    </div>
  );
};

// 示例2: 自动记录当前页面访问
export const AutoLogExample: React.FC = () => {
  // 组件加载时自动记录访问
  const { isLogging } = useAutoLogVisit({
    contentType: ContentType.HomePage,
  });

  return (
    <div>
      <h1>首页</h1>
      {isLogging && <p>正在记录访问...</p>}
    </div>
  );
};

// 示例3: 文档页面自动记录
export const DocumentPageExample: React.FC<{ documentId: number }> = ({ documentId }) => {
  // 自动记录文档访问
  const { isLogging } = useLogDocumentVisit(documentId);

  return (
    <div>
      <h1>文档页面</h1>
      <p>文档ID: {documentId}</p>
      {isLogging && <p>正在记录文档访问...</p>}
    </div>
  );
};

// 示例4: 公告页面自动记录
export const NoticePageExample: React.FC<{ noticeId: number }> = ({ noticeId }) => {
  // 自动记录公告访问
  const { isLogging } = useLogNoticeVisit(noticeId);

  return (
    <div>
      <h1>公告页面</h1>
      <p>公告ID: {noticeId}</p>
      {isLogging && <p>正在记录公告访问...</p>}
    </div>
  );
};

// 示例5: 首页自动记录
export const HomePageExample: React.FC = () => {
  // 自动记录首页访问
  const { isLogging } = useLogHomePageVisit();

  return (
    <div>
      <h1>欢迎来到首页</h1>
      {isLogging && <p>正在记录首页访问...</p>}
    </div>
  );
};

// 示例6: 带自定义参数的手动记录
export const CustomLogExample: React.FC = () => {
  const { logVisit, isLogging } = useLogVisit();

  const handleCustomLog = async () => {
    await logVisit({
      pageUrl: '/custom-page',
      contentType: ContentType.Document,
      contentId: 456,
      userAgent: 'Custom User Agent String',
      referrer: 'https://example.com/previous-page',
    });
  };

  return (
    <div>
      <button onClick={handleCustomLog} disabled={isLogging}>
        {isLogging ? '记录中...' : '自定义记录访问'}
      </button>
    </div>
  );
};

// 示例7: 在路由组件中使用
export const RouteComponentExample: React.FC = () => {
  // 根据当前路由自动判断内容类型
  const getCurrentContentType = (): ContentType => {
    const path = window.location.pathname;
    if (path.includes('/documents/')) return ContentType.Document;
    if (path.includes('/notices/')) return ContentType.Notice;
    return ContentType.HomePage;
  };

  const { logVisit } = useLogVisit();

  React.useEffect(() => {
    // 页面加载时记录访问
    logVisit({
      pageUrl: window.location.pathname + window.location.search,
      contentType: getCurrentContentType(),
      // 如果是文档或公告页面，可以从URL中提取ID
      contentId: extractIdFromUrl(),
    });
  }, [logVisit]);

  const extractIdFromUrl = (): number | undefined => {
    const path = window.location.pathname;
    const match = path.match(/\/(?:documents|notices)\/(\d+)/);
    return match ? parseInt(match[1], 10) : undefined;
  };

  return (
    <div>
      <h1>路由组件示例</h1>
      <p>当前路径: {window.location.pathname}</p>
    </div>
  );
};
