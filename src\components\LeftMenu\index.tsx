import { MenuType } from '@/typings/MenuType';
import { DownloadOutlined, SearchOutlined } from '@ant-design/icons';
import {
  FormattedMessage,
  useIntl,
  useLocation,
  useNavigate,
  useParams,
} from '@umijs/max';
import {
  useClickAway,
  useCreation,
  useMemoizedFn,
  useUpdateEffect,
} from 'ahooks';
import { Empty, Input, Menu, MenuProps, Skeleton } from 'antd';
import { createStyles, cx } from 'antd-style';
import { useEffect, useRef, useState } from 'react';
import useMenu from './hooks/useMenu';

const useStyles = createStyles(({ css, prefixCls }) => ({
  menuWrapper: css`
    position: sticky;
    top: 62px;
    height: calc(100vh - 58px);
    padding: 10px 20px 0;
    border-right: 1px solid rgba(229, 229, 229, 1);
    background-color: #fff;
    overflow: hidden;
  `,
  container: css`
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100% - 32px - 20px);
  `,
  menu: css`
    overflow: auto;
    flex: 1;
    margin-top: 10px;
    &.ant-menu {
      border-inline-end: 0;
    }
    .${prefixCls}-menu
      .${prefixCls}-menu-submenu-expand-icon,
      .${prefixCls}-menu
      .${prefixCls}-menu-submenu-arrow {
      left: 10px;
    }
    .${prefixCls}-menu .${prefixCls}-menu-submenu-arrow:before {
      transform: rotate(135deg) translateX(2.5px);
    }
    .${prefixCls}-menu .${prefixCls}-menu-submenu-arrow:after {
      transform: rotate(45deg) translateX(-2.5px);
    }
    .ant-menu
      .ant-menu-submenu-open.ant-menu-submenu-inline
      > .ant-menu-submenu-title
      > .ant-menu-submenu-arrow::before {
      transform: rotate(225deg) translateX(2.5px);
    }
    .ant-menu
      .ant-menu-submenu-open.ant-menu-submenu-inline
      > .ant-menu-submenu-title
      > .ant-menu-submenu-arrow::after {
      transform: rotate(135deg) translateX(-2.5px);
    }
    .ant-menu
      .ant-menu-submenu-open.ant-menu-submenu-inline
      > .ant-menu-submenu-title
      > .ant-menu-submenu-arrow {
      transform: translateY(2px);
    }
    .ant-menu-inline.ant-menu-root .ant-menu-item,
    .ant-menu-inline.ant-menu-root .ant-menu-submenu-title {
      flex-direction: row-reverse;
      gap: 10px;
    }
    .ant-menu-light.ant-menu-root.ant-menu-inline {
      border-inline-end: 0;
    }
    .ant-menu-light.ant-menu-inline .ant-menu-sub.ant-menu-inline,
    .ant-menu-light > .ant-menu.ant-menu-inline .ant-menu-sub.ant-menu-inline {
      background-color: #fff;
    }
  `,
  search: css`
    position: relative;
  `,
  searchResult: css`
    position: absolute;
    top: 33px;
    width: 100%;
    height: 180px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 10px;
    border: 1px solid #ccc;
    overflow: auto;
    z-index: 999;
    visibility: hidden;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.2s;
    &.active {
      visibility: visible;
      opacity: 1;
      transform: translateY(0);
    }
  `,
  searchResultTitle: css`
    width: 100%;
    cursor: pointer;
    font-size: 14px;
    padding: 10px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    &:hover {
      background-color: #f0f0f0;
    }
  `,
  des: css`
    margin-bottom: 4px;
    font-size: 12px;
    color: #999;
  `,
  download: css`
    width: 100%;
    border-top: 1px solid #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    a {
      display: block;
      padding: 8px;
      color: #000;
      font-size: 14px;
      font-weight: 600;
      text-decoration: none;
    }
  `,
}));

const LeftMenu = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const intl = useIntl();
  const placeholder = intl.formatMessage({
    id: 'menu.search.placeholder',
  });

  const empty = intl.formatMessage({
    id: 'menu.search.options.empty',
  });

  const params = useParams();
  const [url, setUrl] = useState('');

  useEffect(() => {
    const lang = params.lang || 'zh-cn';
    const pdf_url = lang === 'zh-cn' ? IntroductionPDF_ZH : IntroductionPDF_EN;
    setUrl(pdf_url);
  }, [params.lang]);
  const {
    items,
    loading: menuLoading,
    queryMenu,
    queryData,
    queryLoading,
    open,
    setOpen,
    stateOpenKeys,
    handleOpenMenu,
    searchValue,
    setSearchValue,
    onOpenChange,
  } = useMenu();

  useUpdateEffect(() => {
    handleOpenMenu(params.slug as string);
  }, [items, params.slug]);

  const onClick: MenuProps['onClick'] = useMemoizedFn(({ key }) => {
    if (key === 'notice-page') {
      navigate(`/${params.lang}/announcements`);
    } else {
      navigate(`/${params.lang}/document/${key}`);
    }
  });

  const handleSearch = useMemoizedFn(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setOpen(true);
      const value = e.target.value;
      setSearchValue(value);
      queryMenu(value);
    },
  );

  const ref = useRef<HTMLDivElement>(null);

  useClickAway(() => {
    setOpen(false);
  }, ref);

  const calcSelectedKey = useCreation(() => {
    if (location.pathname.includes(`/${params.lang}/announcements`)) {
      return 'notice-page';
    }
    if (params.slug) {
      return params.slug;
    }
    return '';
  }, [params.lang, params.slug, location.pathname]);

  return (
    <div className={styles.menuWrapper}>
      <div className={styles.search}>
        <Input
          placeholder={placeholder}
          prefix={<SearchOutlined />}
          value={searchValue}
          allowClear
          onChange={handleSearch}
        />
        <div
          ref={ref}
          className={cx(styles.searchResult, {
            ['active']: open,
          })}
        >
          <div className={styles.des}>
            <FormattedMessage id={'menu.search.options.result'} />
          </div>
          <Skeleton loading={queryLoading}>
            {queryData?.length ? (
              queryData?.map((item: MenuType) => (
                <div
                  key={item.Id}
                  title={item.Name}
                  className={styles.searchResultTitle}
                  onClick={() => {
                    navigate(`/${params.lang}/document/${item.DocumentSlug}`);
                    setOpen(false);
                    handleOpenMenu(item.Id.toString());
                  }}
                >
                  {item.Name}
                </div>
              ))
            ) : (
              <Empty description={empty} />
            )}
          </Skeleton>
        </div>
      </div>

      <div className={styles.container}>
        <div className={styles.menu}>
          <Skeleton loading={menuLoading}>
            <Menu
              theme={'light'}
              onClick={onClick}
              openKeys={stateOpenKeys}
              onOpenChange={onOpenChange}
              selectedKeys={[calcSelectedKey]}
              mode="inline"
              items={items}
            />
          </Skeleton>
        </div>
        <div className={styles.download}>
          <a href={url} target="_blank" rel="noreferrer">
            <DownloadOutlined /> <FormattedMessage id={'download.title'} />
          </a>
        </div>
      </div>
    </div>
  );
};

export default LeftMenu;
