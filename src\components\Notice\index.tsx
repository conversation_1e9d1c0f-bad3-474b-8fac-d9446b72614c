import { Notice } from '@/services/api';
import { DoubleRightOutlined, SoundTwoTone } from '@ant-design/icons';
import { FormattedMessage, Link, useParams } from '@umijs/max';
import { Col, Flex, Row } from 'antd';
import { createStyles } from 'antd-style';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { FC, memo } from 'react';

dayjs.extend(customParseFormat);

interface NoticeProps {
  items: Notice[];
  showMore?: boolean;
}

const useStyles = createStyles(({ css }) => ({
  header: css`
    background: rgb(195, 210, 247);
    padding: 12px 20px;
    color: rgba(56, 56, 56, 1);
    font-weight: 500;
  `,
  item: css`
    padding: 12px 20px;
    border-bottom: 1px solid rgba(229, 229, 229, 1);
    color: rgba(56, 56, 56, 1);
  `,
  title: css`
    font-size: 30px;
    font-weight: 500;
    color: rgba(56, 56, 56, 1);
  `,
}));

const NoticeList: FC<NoticeProps> = ({ items = [], showMore = false }) => {
  const { styles } = useStyles();
  const params = useParams();
  return (
    <Flex vertical style={{ width: '100%' }} gap={14}>
      <Flex gap={10} justify="space-between" align="center">
        <Flex gap={10}>
          <SoundTwoTone style={{ fontSize: 26 }} />
          <div className={styles.title}>
            <FormattedMessage id={'notice.title'} />
          </div>
        </Flex>
        {showMore && (
          <Flex style={{ alignSelf: 'flex-end' }}>
            <Link to={`/${params.lang}/announcements`}>
              <FormattedMessage id={'notice.more'} />
              <DoubleRightOutlined style={{ fontSize: 10 }} />
            </Link>
          </Flex>
        )}
      </Flex>

      <Flex vertical style={{ width: '100%' }}>
        <Row className={styles.header}>
          {/* <Col span={4}>工作区</Col> */}
          <Col span={22}>
            <FormattedMessage id={'notice.table.columns.title'} />
          </Col>
          <Col span={2}>
            <FormattedMessage id={'notice.table.columns.updatedTime'} />
          </Col>
        </Row>
        {items?.map((item, index) => (
          <Row key={index} className={styles.item}>
            <Col span={22}>
              <Link to={`/${params.lang}/announcements/${item.Id}`}>
                {item.Title}
              </Link>
            </Col>
            {/* <Col span={16}>{item.Content}</Col> */}
            <Col span={2}>{dayjs(item.UpdatedTime).format('YYYY-MM-DD')}</Col>
          </Row>
        ))}
      </Flex>
    </Flex>
  );
};

export default memo(NoticeList);
