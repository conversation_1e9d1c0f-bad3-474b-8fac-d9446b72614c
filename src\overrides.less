#root {
  min-width: 1280px !important;
  font-size: 14px !important;

  /* 设置字体 */
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif !important;
}

/* 整个滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* 定义滑块（滚动条滑动部分） */
::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 hsla(0deg, 0%, 94.1%, 50%) !important;
  border-radius: 4px !important;
  background-color: rgba(0, 0, 0, 30%) !important;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

/* 设置鼠标移入时的滑块颜色 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 50%) !important;
}
