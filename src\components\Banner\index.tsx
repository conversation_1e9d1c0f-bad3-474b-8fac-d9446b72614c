import { FormattedMessage, useIntl } from '@umijs/max';
import { Flex } from 'antd';
import { createStyles } from 'antd-style';

const useStyles = createStyles(({ css }) => ({
  div: css`
    position: relative;
    width: 100%;
    height: 300px;
    padding: 0 120px;
    background: url(${require('@/assets/banner-bg.png')}) no-repeat;
    background-size: cover;
    color: rgba(99, 99, 99, 1);
    h2 {
      font-size: 36px;
      color: rgba(56, 56, 56, 1);
    }
    p {
      width: 60%;
    }
    img {
      width: 250px;
      height: 250px;
    }
    .disclaimer {
      width: 100%;
      margin-bottom: 15px;
      justify-self: flex-start;
    }
  `,
  h2: {
    fontSize: '24px',
    fontWeight: 'bold',
    lineHeight: '32px',
    color: '#333',
  },
}));

const Banner = () => {
  const { styles } = useStyles();
  const intl = useIntl();

  return (
    <Flex
      vertical
      justify="space-between"
      align="center"
      className={styles.div}
    >
      <Flex justify="space-between" align="center" style={{ width: '100%' }}>
        <div>
          <h2>{intl.formatMessage({ id: 'website.title' })}</h2>
          <p>{intl.formatMessage({ id: 'banner.description' })}</p>
        </div>
        <img src={require('@/assets/banner-img.png')} />
      </Flex>

      <div className="disclaimer">
        <FormattedMessage id="disclaimer.title.0" />
        <a
          href="https://support.azure.cn/support/support-azure"
          target="_blank"
          rel="noreferrer"
        >
          <FormattedMessage id="disclaimer.title.1" />
        </a>
        <FormattedMessage id="disclaimer.title.2" />
      </div>
    </Flex>
  );
};

export default Banner;
