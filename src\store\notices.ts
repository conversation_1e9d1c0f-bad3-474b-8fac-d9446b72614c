import { getNotices, Notice } from '@/services/api';
import { persist } from 'valtio-persist';
import { useProxy } from 'valtio/utils';

export type NoticesState = {
  list: Notice[];
};

const { store: noticesState } = await persist<NoticesState>(
  {
    list: [],
  },
  'notices',
);

const useNoticesStore = () => useProxy<NoticesState>(noticesState);

const getNoticesList = async () => {
  try {
    const result = await getNotices();
    noticesState.list = result.Data;
  } catch (e) {
    console.error(e);
  }
};

export { getNoticesList };

export default useNoticesStore;
