import { FormattedMessage } from '@umijs/max';
import { Flex, Space } from 'antd';
import { createStyles } from 'antd-style';
import { memo } from 'react';

const useStyles = createStyles(({ css }) => ({
  title: css`
    font-size: 30px;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    margin-top: 15px;
    margin-bottom: 0;
  `,
  info: css`
    font-size: 14px;
    color: rgba(128, 128, 128, 1);
    margin-top: 15px;
    margin-bottom: 30px;
  `,
}));

interface ArticleHeaderProps {
  author?: string;
  date?: string;
  title: string;
}

const ArticleHeader: React.FC<ArticleHeaderProps> = ({
  author,
  date,
  title,
}) => {
  const { styles } = useStyles();
  return (
    <Flex vertical>
      <h1 className={styles.title}>{title}</h1>
      {date ? (
        <div className={styles.info}>
          <Space size={5}>
            <span>
              <FormattedMessage id={'publish.title'} />：
            </span>
            <span>{new Date(date).toLocaleDateString()}</span>
          </Space>
        </div>
      ) : null}
    </Flex>
  );
};

export default memo(ArticleHeader);
