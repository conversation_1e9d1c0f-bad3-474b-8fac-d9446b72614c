import MarkdownContent from '@/components/MarkdownContent';
import ArticleHeader from '@/pages/Documents/components/ArticleHeader';
import { FormattedMessage, Link, useIntl, useParams } from '@umijs/max';
import { Breadcrumb, Flex, Skeleton } from 'antd';
import useNotice from '../hooks/useNotice';

const NoticeDetail = () => {
  const params = useParams();
  const intl = useIntl();

  const breadcrumb = intl.formatMessage({
    id: 'notice.breadcrumb',
  });

  const { data, loading } = useNotice(params.id as string);

  return (
    <Flex vertical gap={24} style={{ width: '100%', padding: '0 24px' }}>
      <Skeleton loading={loading}>
        <Flex justify="space-between" align="center">
          <Breadcrumb
            items={[
              { title: breadcrumb, href: `/${params.lang}/announcements` },
              { title: data?.Data.Title || '' },
            ]}
          />
          <Link to={`/${params.lang}/announcements`}>
            <FormattedMessage id={'notice.back'} />
          </Link>
        </Flex>

        <ArticleHeader
          title={data?.Data.Title || ''}
          date={data?.Data?.UpdatedTime || ''}
        />
        <MarkdownContent content={data?.Data?.Content || ''} />
      </Skeleton>
    </Flex>
  );
};

export default NoticeDetail;
