import { RequestConfig, RuntimeAntdConfig } from '@umijs/max';
import { requestConfig } from './requestConfig';

export const antd: RuntimeAntdConfig = (memo) => {
  // memo.locale ??= zhCN; // 配置 antd 的语言
  memo.theme ??= {
    token: {
      colorPrimary: '#114bed',
      colorInfo: '#114bed',
      colorError: '#ff6d4d',
      colorSuccess: '#43cf7c',
      colorWarning: '#ffc300',
      colorTextBase: '#383838',
    },
    components: {
      Input: {
        borderRadius: 4,
      },
      Menu: {
        itemHeight: 32,
        itemMarginBlock: 2,
        itemBorderRadius: 4,
        itemSelectedBg: '#e7edfd',
        itemSelectedColor: '#114BED',
        subMenuItemSelectedColor: '#114BED',
      },
    },
  };
  // memo.theme.algorithm = theme.darkAlgorithm; // 配置 antd5 的预设 dark 算法

  return memo;
};

export const request: RequestConfig = {
  ...requestConfig,
};

const langMap: Record<string, string> = {
  'zh-cn': 'zh-CN',
  'en-us': 'en-US',
};

export const locale = {
  getLocale() {
    const { pathname } = window.location;
    if (!pathname.startsWith('/zh-cn') && !pathname.startsWith('/en-us')) {
      return 'zh-CN'; // 默认语言
    }
    return langMap[pathname.split('/')[1]];
  },
};
