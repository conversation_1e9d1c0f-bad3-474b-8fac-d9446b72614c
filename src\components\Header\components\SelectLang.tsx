import { ReactComponent as LanguageSVG } from '@/assets/language.svg';
import { DownOutlined } from '@ant-design/icons';
import { setLocale, useLocation, useNavigate } from '@umijs/max';
import { useSafeState } from 'ahooks';
import { Dropdown, Flex } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';

const useStyles = createStyles(({ css, token }) => ({
  language: css`
    color: ${token.colorPrimary};
    cursor: pointer;
  `,
}));

const langMap: Record<string, string> = {
  'zh-CN': 'zh-cn',
  'en-US': 'en-us',
  'zh-cn': 'zh-CN',
  'en-us': 'en-US',
};

const SelectLang = () => {
  const { styles } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedKey, setSelectedKey] = useSafeState('zh-CN');

  useEffect(() => {
    const lang = location.pathname.split('/')[1] || 'zh-cn';
    setSelectedKey(langMap[lang]);
    setLocale(langMap[lang], false);
  }, [location.pathname, langMap]);

  return (
    <Dropdown
      menu={{
        items: [
          { label: '中文', key: 'zh-CN' },
          { label: 'English', key: 'en-US' },
        ],
        onClick: ({ key }) => {
          setSelectedKey(key);
          setLocale(key, false);
          // 这里需要根据实际的路由进行修改
          navigate(location.pathname.replace(/^\/[^/]+/, `/${langMap[key]}`));
        },
        selectedKeys: [selectedKey],
      }}
      trigger={['click']}
    >
      <Flex align="center" gap={4} className={styles.language}>
        <LanguageSVG />
        {selectedKey === 'zh-CN' ? '中文/Chinese' : 'English/英文'}
        <DownOutlined style={{ color: '#114bed', fontSize: 12 }} />
      </Flex>
    </Dropdown>
  );
};

export default SelectLang;
