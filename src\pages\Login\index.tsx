import Header from '@/components/Header';
import { getUserInfo } from '@/store/user';
import {
  FormattedMessage,
  getLocale,
  useIntl,
  useNavigate,
  useParams,
  useSearchParams,
} from '@umijs/max';
import { useMemoizedFn, useUnmount, useUpdateEffect } from 'ahooks';
import {
  Button,
  Checkbox,
  Flex,
  Form,
  Input,
  message,
  Spin,
  Tooltip,
} from 'antd';
import { createStyles } from 'antd-style';
import { useState } from 'react';
import useLogin from './hooks/useLogin';

const useStyles = createStyles(({ css }) => ({
  container: css`
    width: 100%;
    min-height: 100vh;
    background: url(${require('@/assets/loginBg.png')}) center no-repeat;
  `,
  header: css`
    width: 100%;
  `,
  form: css`
    width: 600px;
    padding: 60px 100px;
    background: rgba(255, 255, 255, 1);
    border-radius: 20px;
  `,

  title: css`
    font-size: 28px;
    font-weight: 600;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    vertical-align: top;
  `,
  captcha: css`
    width: 100px;
    height: 32px;
  `,
  footer: css`
    font-size: 12px;
    color: rgba(56, 56, 56, 1);
  `,
}));
const Login = () => {
  const { styles } = useStyles();
  const params = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [messageApi, contextHolder] = message.useMessage();
  const [form] = Form.useForm();

  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  const intl = useIntl();

  const accountPlaceholder = intl.formatMessage({
    id: 'login.username.placeholder',
  });

  const accountRequiredError = intl.formatMessage({
    id: 'login.username.required',
  });

  const passwordRequiredError = intl.formatMessage({
    id: 'login.password.required',
  });

  const captchaRequiredError = intl.formatMessage({
    id: 'login.captcha.required',
  });

  const privacyUrl = intl.formatMessage({ id: 'login.privacy.url' });

  const [loading, setLoading] = useState(false);

  const agree = Form.useWatch(['agree'], form);

  const { captchaData, captchaLoading, refresh } = useLogin();

  const onFinish = useMemoizedFn((values: any) => {
    setLoading(true);
    getUserInfo(
      values.username,
      values.password,
      captchaData?.Data.uuid || '',
      values.captcha,
    )
      .then((res) => {
        console.log(searchParams.get('from'));
        if (res.IsSuccess) {
          if (searchParams.get('from')) {
            navigate(`${searchParams.get('from')}`);
          } else {
            navigate(`/${params.lang}/home`);
          }
        } else {
          setShowError(true);
          setErrorMessage(res.Msg);
          refresh();
        }
      })
      .finally(() => {
        setLoading(false);
      });
  });

  useUpdateEffect(() => {
    form.validateFields();
  }, [getLocale()]);

  useUnmount(() => {
    setShowError(false);
    setErrorMessage('');
    messageApi.destroy();
  });
  return (
    <Flex className={styles.container} vertical justify="center" align="center">
      <div className={styles.header}>
        <Header />
      </div>
      <Flex justify="space-around" align="center" style={{ width: '90%' }}>
        <div>
          <img src={require('@/assets/loginImage4.png')} />
        </div>
        <div className={styles.form}>
          <h2 className={styles.title}>
            <FormattedMessage id="website.title" />
          </h2>
          <Form form={form} layout="vertical" onFinish={onFinish}>
            <Form.Item
              label={<FormattedMessage id="login.username.title" />}
              name={'username'}
              rules={[{ required: true, message: accountRequiredError }]}
              style={{ marginBottom: 15 }}
            >
              <Input type="text" placeholder={accountPlaceholder} />
            </Form.Item>
            <Form.Item
              label={<FormattedMessage id="login.password.title" />}
              name={'password'}
              rules={[{ required: true, message: passwordRequiredError }]}
              style={{ marginBottom: 15 }}
            >
              <Input.Password />
            </Form.Item>
            <Form.Item
              label={<FormattedMessage id="login.captcha.title" />}
              required={true}
              style={{ marginBottom: 15 }}
            >
              <Flex align="center" gap={10}>
                <Form.Item
                  noStyle
                  name={'captcha'}
                  rules={[{ required: true, message: captchaRequiredError }]}
                >
                  <Input />
                </Form.Item>
                <Tooltip placement="top" title={'点击刷新'}>
                  <Spin spinning={captchaLoading}>
                    {captchaData?.Data?.img && (
                      <img
                        onClick={() => {
                          refresh();
                        }}
                        className={styles.captcha}
                        src={`data:image/png;base64,${captchaData?.Data?.img}`}
                      />
                    )}
                  </Spin>
                </Tooltip>
              </Flex>
            </Form.Item>
            <Form.Item noStyle>
              {showError && <div style={{ color: 'red' }}>{errorMessage}</div>}
            </Form.Item>
            <Form.Item style={{ marginBottom: 15 }}>
              <>
                <Form.Item name={'agree'} valuePropName="checked" noStyle>
                  <Checkbox>
                    <FormattedMessage id="login.agree" />
                  </Checkbox>
                </Form.Item>
                <a href={privacyUrl} target="_blank" rel="noreferrer">
                  <FormattedMessage id="login.privacy.title" />
                </a>
              </>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                block
                htmlType="submit"
                loading={loading}
                disabled={!agree}
              >
                <FormattedMessage id="login.button" />
              </Button>
            </Form.Item>
          </Form>
          <Flex vertical gap={4} className={styles.footer}>
            <div>
              <FormattedMessage id="login.footer.remind" />：
            </div>
            <div>
              <FormattedMessage id="login.footer.disclaimer.0" />
              <a href={REGISTER_URL} target="_blank" rel="noreferrer">
                <FormattedMessage id={'login.footer.disclaimer.1'} />
              </a>
              <FormattedMessage id="login.footer.disclaimer.2" />
            </div>
            {/* <div>
              <a href="">
                <FormattedMessage id="login.footer.guide" />
              </a>
            </div> */}
          </Flex>
        </div>
      </Flex>
      {contextHolder}
    </Flex>
  );
};

export default Login;
