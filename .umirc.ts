import { defineConfig } from '@umijs/max';

export default defineConfig({
  title: '21VCA-E Playbook',
  define: {
    REGISTER_URL:
      process.env.UMI_ENV === 'uat'
        ? 'https://nceuat.21vbluecloud.com/customized/ncei-project/'
        : 'https://nce.21vbluecloud.com/customized/ncei-project/',
    IntroductionPDF_ZH:
      process.env.UMI_ENV === 'uat'
        ? 'https://learnstorage.blob.core.chinacloudapi.cn/publiclearn/pdf/Playbook-21VCA-E_Chinese.pdf'
        : 'https://learnstorageprod.blob.core.chinacloudapi.cn/learnpublic/pdf/Playbook-21VCA-E_Chinese.pdf',
    IntroductionPDF_EN:
      process.env.UMI_ENV === 'uat'
        ? 'https://learnstorage.blob.core.chinacloudapi.cn/publiclearn/pdf/Playbook-21VCA-E_English.pdf'
        : 'https://learnstorageprod.blob.core.chinacloudapi.cn/learnpublic/pdf/Playbook-21VCA-E_English.pdf',
  },
  antd: { configProvider: {} },
  request: { dataField: 'Data' },
  metas: [{ name: 'viewport', content: 'width=device-width, initial-scale=1' }],
  locale: {
    default: 'zh-CN',
    antd: true,
    baseNavigator: true,
    useLocalStorage: false,
  },
  model: {},
  esbuildMinifyIIFE: true,
  routes: [
    {
      path: '/',
      redirect: '/zh-cn/home',
    },
    {
      path: '/:lang',
      redirect: '/:lang/home',
    },
    {
      name: '登录',
      path: '/:lang/login',
      component: '@/pages/Login',
      layout: false,
    },
    {
      name: '首页',
      path: '/:lang/home',
      component: '@/pages/Home',
    },
    {
      name: '文档',
      path: '/:lang/document/:slug',
      component: '@/pages/Documents',
    },
    {
      name: '公告',
      path: '/:lang/announcements',
      component: '@/pages/Notice',
    },
    {
      name: '公告详情',
      path: '/:lang/announcements/:id',
      component: '@/pages/Notice/Detail',
    },
    {
      name: '免责声明',
      path: '/:lang/disclaimer',
      component: '@/pages/Disclaimer',
    },
    { path: '/*', component: '@/pages/404' },
  ],
  proxy: {
    '/api': {
      // target: 'http://172.31.210.108:8066',
      target: 'https://learnapi-test.chinacloudsites.cn', // uat
      changeOrigin: true,
    },
  },
  npmClient: 'pnpm',
});
