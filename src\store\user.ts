import { login } from '@/pages/Login/services';
import { history } from '@umijs/max';
import { derive } from 'derive-valtio';
import { snapshot } from 'valtio';
import { persist } from 'valtio-persist';
import { useProxy } from 'valtio/utils';

export type UserInfo = {
  AccessToken: string;
  RefreshToken: string;
  AccessExpireMinutes: string;
  RefreshExpireMinutes: string;
  UserId: string;
  UserName: string;
  Roles: {
    Id: number;
    Name: string;
  }[];
  LoginDate: number;
};

const { store: userStore } = await persist<UserInfo>(
  {
    AccessToken: '',
    RefreshToken: '',
    AccessExpireMinutes: '',
    RefreshExpireMinutes: '',
    UserId: '',
    UserName: '',
    Roles: [],
    LoginDate: 0,
  },
  'user',
);

const deriveuserStore = derive({
  isLogin: (get) => get(userStore).AccessToken,
  showVisit: (get) => get(userStore).Roles.some((role) => role.Id === 1 || role.Id === 3),
});

const userState = snapshot(userStore);
const useUserStore = () => useProxy(userStore);

const getUserInfo = async (
  username: string,
  password: string,
  uuid: string,
  code: string,
) => {
  const result = await login({ username, password, uuid, code });

  if (result.IsSuccess) {
    userStore.AccessToken = result.Data.AccessToken;
    userStore.RefreshToken = result.Data.RefreshToken;
    userStore.AccessExpireMinutes = result.Data.AccessExpireMinutes;
    userStore.RefreshExpireMinutes = result.Data.RefreshExpireMinutes;
    userStore.UserId = result.Data.UserId;
    userStore.UserName = result.Data.UserName;
    userStore.Roles = result.Data.Roles;
    userStore.LoginDate = Date.now();
  }

  return result;
};

const userLogout = () => {
  userStore.AccessToken = '';
  userStore.RefreshToken = '';
  userStore.AccessExpireMinutes = '';
  userStore.RefreshExpireMinutes = '';
  userStore.UserId = '';
  userStore.UserName = '';
  userStore.Roles = [];

  history.push(`/${window.location.pathname.split('/')[1]}/home`);
};

const checkTokenExpiration = () => {
  const token = userStore.AccessToken;
  const loginTime = userStore.LoginDate;
  const expireDuration = Number(userStore.AccessExpireMinutes) * 60 * 1000; // 24小时有效期

  // 如果Token或时间不存在，视为未登录
  if (!token || !loginTime) {
    return true;
  }

  // 检查是否过期
  const isExpired = Date.now() - Number(loginTime) > expireDuration;
  return isExpired;
};

export {
  checkTokenExpiration,
  deriveuserStore,
  getUserInfo,
  userLogout,
  userState,
  userStore,
};

export default useUserStore;
