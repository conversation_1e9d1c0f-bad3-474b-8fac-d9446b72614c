import MarkdownContent from '@/components/MarkdownContent';
import { getLocale, useModel, useParams } from '@umijs/max';
import { Breadcrumb, Empty, Flex, Skeleton } from 'antd';
import { createStyles } from 'antd-style';
import { useEffect } from 'react';
import useQueryDocument from '../hooks/useQueryDocument';
import ArticleHeader from './ArticleHeader';

const useStyles = createStyles(({ css }) => ({
  article: css`
    flex: 1;
    padding: 10px 40px 80px;
    display: flex;
    flex-direction: column;
  `,
}));

const Article = () => {
  const { styles } = useStyles();
  const params = useParams();
  const { run, data, loading } = useQueryDocument();
  // const documentId = searchParams.get('d') || '';

  const { breadcrumb, menu } = useModel('global');

  useEffect(() => {
    if (params.slug) {
      run(params.slug);
    }
  }, [params.slug, getLocale()]);

  if (loading)
    return (
      <div className={styles.article}>
        <Skeleton active />
      </div>
    );

  if (!data)
    return (
      <Flex justify="center" align="center" className={styles.article}>
        <Empty />
      </Flex>
    );

  return (
    <div className={styles.article}>
      <Breadcrumb
        items={[
          { title: '21VCA-E Playbook' },
          ...breadcrumb?.map((item) => ({
            title: item,
          })),
        ]}
      />
      <ArticleHeader
        author={data?.Author || ''}
        title={data?.Title || ''}
        date={data?.UpdatedTime || ''}
      />
      <MarkdownContent content={data?.Content} />
    </div>
  );
};

export default Article;
