export default {
  website: {
    title: '21VCA-E Playbook',
  },
  login: {
    username: {
      title: 'Account',
      placeholder: 'User Name/ Email / Mobile',
      required: 'Please enter your account',
    },
    password: { title: 'Password', required: 'Please enter your password' },
    login: 'Partner Support',
    signin: 'Internal Sign In',
    button: 'Sign in',
    logout: 'Sign out',
    captcha: {
      title: 'Verification Code',
      required: 'Please enter the verification code',
    },
    agree: 'I have read and agree',
    privacy: {
      title: '《21Vianet Online Services Privacy Statement》',
      url: 'https://en.21vbluecloud.com/ostpt',
    },
    footer: {
      remind: 'Reminder',
      disclaimer: {
        0: 'The content of this page is confidential. Please do not download or forward it without permission Please click ',
        1: 'here',
        2: ' to register new 21VCA-E account.',
      },
      guide: 'Account Registration Guide',
    },
  },
  banner: {
    description:
      'This Playbook is applicable to Microsoft Azure operated by 21Vianet, providing relevant information for Azure customers and partners who wants to learn about the business model, procurement methods, and post-sales support of the corresponding services. ',
  },
  home: {
    title: '21VCA-E New Commerce Introduction',
    one: {
      title: 'The New Commerce Experience',
      sub: 'Greater standardization of offers and terms gives customers greater choice and flexibility in how/where they buy, while giving partners greater opportunity to sell/upsell to a larger set of existing and new customers.',
      desc: 'Enabling transformation by delivering on our customer promises',
    },
    two: {
      title: 'New Commerce Experience on Microsoft Azure Operated by 21Vianet',
      sub: 'New Capabilities from 21VCA-E',
      desc: '',
    },
    three: {
      title: 'End to End Process – Faster Provisioning',
      sub: '',
      desc: '',
    },
  },
  loginHome: {
    0: {
      title: '21VCA-E New Commerce Introduction',
      desc: 'Greater standardization of offers and terms gives customers greater choice and flexibility in how/where they buy, while giving partners greater opportunity to sell/upsell to a larger set of existing and new customers.',
    },
    1: {
      title: 'Partner Onboarding',
      desc: 'Launch Your Cloud Partnership Journery',
    },
    2: {
      title: 'Order to Cash',
      desc: 'Deals that successfully pass this validation will be provisioned within a few of hours, resulting in a remarkable 6-8 times reduction in waiting time compared to the existing OSPA process. Whether you are partner or customer, all can clearly perceive the process simplification and efficiency improvement brought about by the new business model of Microsoft Azure operated by 21Vianet from the perspective of business processes.',
    },
    3: {
      title: 'Reservation Instance (RI)',
      desc: 'Azure Reservation (RI) helps you save money by committing to one-year or three-year plans for multiple products. By committing to prepayment, resources can be purchased at a discounted price. Reservation can significantly reduce the cost of resources, up to 72% of the pay as you go price. Reservation provides billing discounts and does not affect the runtime status of resources. After purchasing the Reservation, the discount will be automatically applied to the matching resources.',
    },
  },
  disclaimer: {
    title: [
      'Disclaimer: This content is provided for customer self-service reference only and may not cover all specific situations or the latest updates. Further assistance or in case of urgent issues, please ',
      'submit a ticket',
      ' online to contact our customer support team for guidance.',
    ],
  },
  menu: {
    search: {
      placeholder: 'Search Documentation',
      options: {
        result: 'Search Results',
        empty: 'No related documents found',
      },
    },
  },
  publish: {
    title: 'Published Date',
  },
  download: {
    title: 'Download PDF',
  },
  notice: {
    title: 'Partners latest announcements',
    breadcrumb: 'Announcement',
    table: {
      columns: {
        title: 'Announcement',
        content: 'Content',
        updatedTime: 'Updated Date',
      },
    },
    more: 'More',
    back: 'Back',
  },
  footer: {
    disclaimer: 'Disclaimer',
  },
};
