import { createStyles } from 'antd-style';

const useStyles = createStyles(({ token }) => ({
  container: {
    width: '80%',
    margin: '0 auto',
    marginTop: 48,
    span: {
      fontSize: 16,
      fontWeight: 600,
    },
    a: {
      color: token.colorPrimary,
      textDecoration: 'none',
    },
  },
}));

const Disclaimer = () => {
  const { styles } = useStyles();
  // useEffect(() => {
  //   fetch('/privacy.md')
  //     .then((res) => res.text())
  //     .then((text) => {
  //       setContent(text);
  //     });
  // }, []);

  return (
    <div className={styles.container}>
      <span>免责声明：</span>
      本内容仅作为客户自助参考使用，所提供的内容可能无法涵盖所有具体情况或最新变更。如需进一步协助或遇到紧急问题，请在线
      <a
        href="https://support.azure.cn/support/support-azure"
        target="_blank"
        rel="noreferrer"
      >
        提交工单
      </a>
      以联系我们的客服支持团队来获得指导。
    </div>
  );
};

export default Disclaimer;
